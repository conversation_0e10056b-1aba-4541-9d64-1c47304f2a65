#!/usr/bin/env node

// Manual test script to demonstrate how to interact with the MCP server
import { spawn } from 'child_process';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

console.log('🧪 Manual MySQL MCP Server Test');
console.log('This demonstrates how AI assistants interact with your server\n');

// Start the server
const serverProcess = spawn('node', [join(__dirname, 'dist/index.js')], {
  stdio: ['pipe', 'pipe', 'pipe']
});

let messageId = 1;

function sendMessage(method, params = {}) {
  const message = {
    jsonrpc: '2.0',
    id: messageId++,
    method: method,
    params: params
  };
  
  console.log(`📤 Sending: ${method}`);
  console.log(JSON.stringify(message, null, 2));
  
  serverProcess.stdin.write(JSON.stringify(message) + '\n');
}

let output = '';
serverProcess.stdout.on('data', (data) => {
  output += data.toString();
  
  // Process complete JSON responses
  const lines = output.split('\n');
  for (let i = 0; i < lines.length - 1; i++) {
    const line = lines[i].trim();
    if (line.startsWith('{')) {
      try {
        const response = JSON.parse(line);
        console.log(`📥 Response (ID ${response.id}):`);
        console.log(JSON.stringify(response, null, 2));
        console.log('─'.repeat(50));
      } catch (e) {
        // Skip invalid JSON
      }
    }
  }
  output = lines[lines.length - 1]; // Keep incomplete line
});

serverProcess.stderr.on('data', (data) => {
  console.log('🖥️  Server:', data.toString().trim());
});

// Test sequence
setTimeout(() => {
  console.log('1️⃣  Testing: List available tools');
  sendMessage('tools/list');
}, 1000);

setTimeout(() => {
  console.log('\n2️⃣  Testing: Connect to database');
  sendMessage('tools/call', {
    name: 'mysql_connect',
    arguments: {
      host: 'localhost',
      port: 3306,
      user: 'test_user',
      password: 'test_password',
      database: 'test_db'
    }
  });
}, 3000);

setTimeout(() => {
  console.log('\n3️⃣  Testing: List databases (will fail without real connection)');
  sendMessage('tools/call', {
    name: 'mysql_list_databases',
    arguments: {}
  });
}, 5000);

setTimeout(() => {
  console.log('\n✅ Test complete! This shows how AI assistants communicate with your server.');
  console.log('\n💡 To use with real data:');
  console.log('   1. Update config.json with real MySQL credentials');
  console.log('   2. Configure your AI assistant to use this MCP server');
  console.log('   3. The AI can then query your database safely!');
  
  serverProcess.kill();
  process.exit(0);
}, 7000);

// Cleanup
process.on('SIGINT', () => {
  serverProcess.kill();
  process.exit(0);
});
