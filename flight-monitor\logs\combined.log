{"level":"info","message":"Email service initialized","service":"gmail","timestamp":"2025-06-07 23:36:53"}
{"level":"info","message":"AWS SNS service initialized","service":"flight-monitor","timestamp":"2025-06-07 23:36:53"}
{"level":"info","message":"🔍 Running single flight check...","service":"flight-monitor","timestamp":"2025-06-07 23:36:53"}
{"level":"warn","message":"Flight configurations file not found, creating empty configuration","service":"flight-monitor","timestamp":"2025-06-07 23:36:53"}
{"activeConfigs":0,"configCount":0,"level":"info","message":"Flight Monitor Service initialized","service":"flight-monitor","timestamp":"2025-06-07 23:36:53"}
{"level":"info","message":"No active flight configurations to check","service":"flight-monitor","timestamp":"2025-06-07 23:36:53"}
{"failedChecks":0,"flightsFound":0,"lastCheckTime":"","level":"info","message":"✅ Single check completed:","notificationsSent":0,"service":"flight-monitor","successfulChecks":0,"timestamp":"2025-06-07 23:36:53","totalChecks":0,"uptime":0}
{"level":"info","message":"Email service initialized","service":"gmail","timestamp":"2025-06-07 23:37:21"}
{"level":"info","message":"AWS SNS service initialized","service":"flight-monitor","timestamp":"2025-06-07 23:37:21"}
{"level":"info","message":"🔍 Running single flight check...","service":"flight-monitor","timestamp":"2025-06-07 23:37:21"}
{"count":2,"level":"info","message":"Flight configurations loaded","service":"flight-monitor","timestamp":"2025-06-07 23:37:21"}
{"activeConfigs":1,"configCount":2,"level":"info","message":"Flight Monitor Service initialized","service":"flight-monitor","timestamp":"2025-06-07 23:37:21"}
{"configCount":1,"level":"info","message":"Starting flight availability check","service":"flight-monitor","timestamp":"2025-06-07 23:37:21"}
{"level":"info","message":"Searching flights","searchRequest":{"cabinClass":"ECONOMY","departureDate":"2025-09-19","destinationCode":"NAV","originCode":"IST","passengers":{"adults":1}},"service":"flight-monitor","timestamp":"2025-06-07 23:37:21"}
{"data":"Invalid API Key","level":"error","message":"Turkish Airlines API Response Error Request failed with status code 400","service":"flight-monitor","status":400,"statusText":"Bad Request","timestamp":"2025-06-07 23:37:22"}
{"error":"Request failed with status code 400","level":"error","message":"Flight search failed","searchRequest":{"cabinClass":"ECONOMY","departureDate":"2025-09-19","destinationCode":"NAV","originCode":"IST","passengers":{"adults":1}},"service":"flight-monitor","timestamp":"2025-06-07 23:37:22"}
{"configId":"istanbul-nevsehir-test","error":"Flight search failed: Request failed with status code 400","level":"error","message":"Failed to check flight configuration","service":"flight-monitor","timestamp":"2025-06-07 23:37:22"}
{"level":"info","message":"Flight availability check completed","service":"flight-monitor","timestamp":"2025-06-07 23:37:22"}
{"failedChecks":0,"flightsFound":0,"lastCheckTime":"2025-06-08T03:37:21.935Z","level":"info","message":"✅ Single check completed:","notificationsSent":0,"service":"flight-monitor","successfulChecks":1,"timestamp":"2025-06-07 23:37:22","totalChecks":1,"uptime":0}
{"level":"info","message":"Email service initialized","service":"gmail","timestamp":"2025-06-07 23:38:24"}
{"level":"info","message":"AWS SNS service initialized","service":"flight-monitor","timestamp":"2025-06-07 23:38:24"}
{"level":"info","message":"🔍 Running single flight check...","service":"flight-monitor","timestamp":"2025-06-07 23:38:24"}
{"count":2,"level":"info","message":"Flight configurations loaded","service":"flight-monitor","timestamp":"2025-06-07 23:38:24"}
{"activeConfigs":1,"configCount":2,"level":"info","message":"Flight Monitor Service initialized","service":"flight-monitor","timestamp":"2025-06-07 23:38:24"}
{"configCount":1,"level":"info","message":"Starting flight availability check","service":"flight-monitor","timestamp":"2025-06-07 23:38:24"}
{"level":"info","message":"Searching flights","searchRequest":{"cabinClass":"ECONOMY","departureDate":"2025-09-19","destinationCode":"NAV","originCode":"IST","passengers":{"adults":1}},"service":"flight-monitor","timestamp":"2025-06-07 23:38:24"}
{"data":"Invalid API Secret","level":"error","message":"Turkish Airlines API Response Error Request failed with status code 400","service":"flight-monitor","status":400,"statusText":"Bad Request","timestamp":"2025-06-07 23:38:25"}
{"error":"Request failed with status code 400","level":"error","message":"Flight search failed","searchRequest":{"cabinClass":"ECONOMY","departureDate":"2025-09-19","destinationCode":"NAV","originCode":"IST","passengers":{"adults":1}},"service":"flight-monitor","timestamp":"2025-06-07 23:38:25"}
{"configId":"istanbul-nevsehir-test","error":"Flight search failed: Request failed with status code 400","level":"error","message":"Failed to check flight configuration","service":"flight-monitor","timestamp":"2025-06-07 23:38:25"}
{"level":"info","message":"Flight availability check completed","service":"flight-monitor","timestamp":"2025-06-07 23:38:25"}
{"failedChecks":0,"flightsFound":0,"lastCheckTime":"2025-06-08T03:38:24.431Z","level":"info","message":"✅ Single check completed:","notificationsSent":0,"service":"flight-monitor","successfulChecks":1,"timestamp":"2025-06-07 23:38:25","totalChecks":1,"uptime":0}
{"level":"info","message":"Email service initialized","service":"gmail","timestamp":"2025-06-07 23:41:59"}
{"level":"info","message":"AWS SNS service initialized","service":"flight-monitor","timestamp":"2025-06-07 23:41:59"}
{"level":"info","message":"🔍 Running single flight check...","service":"flight-monitor","timestamp":"2025-06-07 23:41:59"}
{"count":2,"level":"info","message":"Flight configurations loaded","service":"flight-monitor","timestamp":"2025-06-07 23:41:59"}
{"activeConfigs":1,"configCount":2,"level":"info","message":"Flight Monitor Service initialized","service":"flight-monitor","timestamp":"2025-06-07 23:41:59"}
{"configCount":1,"level":"info","message":"Starting flight availability check","service":"flight-monitor","timestamp":"2025-06-07 23:41:59"}
{"level":"info","message":"Searching flights","searchRequest":{"cabinClass":"ECONOMY","departureDate":"2025-09-19","destinationCode":"NAV","originCode":"IST","passengers":{"adults":1}},"service":"flight-monitor","timestamp":"2025-06-07 23:41:59"}
{"data":"Invalid API Secret","level":"error","message":"Turkish Airlines API Response Error Request failed with status code 400","service":"flight-monitor","status":400,"statusText":"Bad Request","timestamp":"2025-06-07 23:41:59"}
{"error":"Request failed with status code 400","level":"error","message":"Flight search failed","searchRequest":{"cabinClass":"ECONOMY","departureDate":"2025-09-19","destinationCode":"NAV","originCode":"IST","passengers":{"adults":1}},"service":"flight-monitor","timestamp":"2025-06-07 23:41:59"}
{"configId":"istanbul-nevsehir-test","error":"Flight search failed: Request failed with status code 400","level":"error","message":"Failed to check flight configuration","service":"flight-monitor","timestamp":"2025-06-07 23:41:59"}
{"level":"info","message":"Flight availability check completed","service":"flight-monitor","timestamp":"2025-06-07 23:41:59"}
{"failedChecks":0,"flightsFound":0,"lastCheckTime":"2025-06-08T03:41:59.088Z","level":"info","message":"✅ Single check completed:","notificationsSent":0,"service":"flight-monitor","successfulChecks":1,"timestamp":"2025-06-07 23:41:59","totalChecks":1,"uptime":0}
{"level":"info","message":"Email service initialized","service":"gmail","timestamp":"2025-06-07 23:45:12"}
{"level":"info","message":"AWS SNS service initialized","service":"flight-monitor","timestamp":"2025-06-07 23:45:12"}
{"level":"info","message":"🔍 Running single flight check...","service":"flight-monitor","timestamp":"2025-06-07 23:45:12"}
{"count":2,"level":"info","message":"Flight configurations loaded","service":"flight-monitor","timestamp":"2025-06-07 23:45:12"}
{"activeConfigs":1,"configCount":2,"level":"info","message":"Flight Monitor Service initialized","service":"flight-monitor","timestamp":"2025-06-07 23:45:12"}
{"configCount":1,"level":"info","message":"Starting flight availability check","service":"flight-monitor","timestamp":"2025-06-07 23:45:12"}
{"level":"info","message":"Searching flights","searchRequest":{"cabinClass":"ECONOMY","departureDate":"2025-09-19","destinationCode":"NAV","originCode":"IST","passengers":{"adults":1}},"service":"flight-monitor","timestamp":"2025-06-07 23:45:12"}
{"data":{"data":{},"message":{"code":"TK-BWS-10000","description":"$.OriginDestinationInformation[0].DepartureDateTime.WindowAfter: is missing and it is not optional, $.OriginDestinationInformation[0].DepartureDateTime.WindowBefore: is missing and it is not optional, $.OriginDestinationInformation[0].DepartureDateTime.Date: does not match the regex pattern ^[0-9]{1,2}?[A-Z]{3}$, $.PassengerTypeQuantity[0].Code: does not have a value in the enumeration [ADULT, CHILD, INFANT, SENIOR, STUDENT, DISABLED, ATTENDANT, TEACHER, adult, child, infant, senior, student, disabled, attendant, teacher, IT_ADULT, IT_CHILD, IT_INFANT, ETHNIC, ETHNIC_CHILD, ETHNIC_INFANT, SEAMAN, LABOR, LABOR_CHILD, LABOR_INFANT, labor, labor_child, labor_infant, SOLDIER, YOUTH, YOUNGADULT, soldier, youth, youngadult]"},"requestId":"000001944287d863-3c2980de","status":"FAILURE"},"level":"error","message":"Turkish Airlines API Response Error Request failed with status code 400","service":"flight-monitor","status":400,"statusText":"Bad Request","timestamp":"2025-06-07 23:45:13"}
{"error":"Request failed with status code 400","level":"error","message":"Flight search failed","searchRequest":{"cabinClass":"ECONOMY","departureDate":"2025-09-19","destinationCode":"NAV","originCode":"IST","passengers":{"adults":1}},"service":"flight-monitor","timestamp":"2025-06-07 23:45:13"}
{"configId":"istanbul-nevsehir-test","error":"Flight search failed: Request failed with status code 400","level":"error","message":"Failed to check flight configuration","service":"flight-monitor","timestamp":"2025-06-07 23:45:13"}
{"level":"info","message":"Flight availability check completed","service":"flight-monitor","timestamp":"2025-06-07 23:45:13"}
{"failedChecks":0,"flightsFound":0,"lastCheckTime":"2025-06-08T03:45:12.909Z","level":"info","message":"✅ Single check completed:","notificationsSent":0,"service":"flight-monitor","successfulChecks":1,"timestamp":"2025-06-07 23:45:13","totalChecks":1,"uptime":0}
{"level":"info","message":"Email service initialized","service":"gmail","timestamp":"2025-06-07 23:46:01"}
{"level":"info","message":"AWS SNS service initialized","service":"flight-monitor","timestamp":"2025-06-07 23:46:01"}
{"level":"info","message":"🔍 Running single flight check...","service":"flight-monitor","timestamp":"2025-06-07 23:46:01"}
{"count":2,"level":"info","message":"Flight configurations loaded","service":"flight-monitor","timestamp":"2025-06-07 23:46:01"}
{"activeConfigs":1,"configCount":2,"level":"info","message":"Flight Monitor Service initialized","service":"flight-monitor","timestamp":"2025-06-07 23:46:01"}
{"configCount":1,"level":"info","message":"Starting flight availability check","service":"flight-monitor","timestamp":"2025-06-07 23:46:01"}
{"level":"info","message":"Searching flights","searchRequest":{"cabinClass":"ECONOMY","departureDate":"2025-09-19","destinationCode":"NAV","originCode":"IST","passengers":{"adults":1}},"service":"flight-monitor","timestamp":"2025-06-07 23:46:01"}
{"data":{"data":{},"message":{"code":"TK-TK-BWS-99999","description":"Unknown Error"},"requestId":"000001944287d863-3c29812c","status":"FAILURE"},"level":"error","message":"Turkish Airlines API Response Error Request failed with status code 500","service":"flight-monitor","status":500,"statusText":"Internal Server Error","timestamp":"2025-06-07 23:46:02"}
{"error":"Request failed with status code 500","level":"error","message":"Flight search failed","searchRequest":{"cabinClass":"ECONOMY","departureDate":"2025-09-19","destinationCode":"NAV","originCode":"IST","passengers":{"adults":1}},"service":"flight-monitor","timestamp":"2025-06-07 23:46:02"}
{"configId":"istanbul-nevsehir-test","error":"Flight search failed: Request failed with status code 500","level":"error","message":"Failed to check flight configuration","service":"flight-monitor","timestamp":"2025-06-07 23:46:02"}
{"level":"info","message":"Flight availability check completed","service":"flight-monitor","timestamp":"2025-06-07 23:46:02"}
{"failedChecks":0,"flightsFound":0,"lastCheckTime":"2025-06-08T03:46:01.136Z","level":"info","message":"✅ Single check completed:","notificationsSent":0,"service":"flight-monitor","successfulChecks":1,"timestamp":"2025-06-07 23:46:02","totalChecks":1,"uptime":1}
{"level":"info","message":"Email service initialized","service":"gmail","timestamp":"2025-06-07 23:46:36"}
{"level":"info","message":"AWS SNS service initialized","service":"flight-monitor","timestamp":"2025-06-07 23:46:36"}
{"level":"info","message":"🔍 Running single flight check...","service":"flight-monitor","timestamp":"2025-06-07 23:46:36"}
{"count":2,"level":"info","message":"Flight configurations loaded","service":"flight-monitor","timestamp":"2025-06-07 23:46:36"}
{"activeConfigs":1,"configCount":2,"level":"info","message":"Flight Monitor Service initialized","service":"flight-monitor","timestamp":"2025-06-07 23:46:36"}
{"configCount":1,"level":"info","message":"Starting flight availability check","service":"flight-monitor","timestamp":"2025-06-07 23:46:36"}
{"level":"info","message":"Searching flights","searchRequest":{"cabinClass":"ECONOMY","departureDate":"2024-12-25","destinationCode":"AYT","originCode":"IST","passengers":{"adults":1}},"service":"flight-monitor","timestamp":"2025-06-07 23:46:36"}
{"data":{"data":{},"message":{"code":"TK-TK-BWS-99999","description":"Unknown Error"},"requestId":"000001944287d863-3c298166","status":"FAILURE"},"level":"error","message":"Turkish Airlines API Response Error Request failed with status code 500","service":"flight-monitor","status":500,"statusText":"Internal Server Error","timestamp":"2025-06-07 23:46:36"}
{"error":"Request failed with status code 500","level":"error","message":"Flight search failed","searchRequest":{"cabinClass":"ECONOMY","departureDate":"2024-12-25","destinationCode":"AYT","originCode":"IST","passengers":{"adults":1}},"service":"flight-monitor","timestamp":"2025-06-07 23:46:36"}
{"configId":"istanbul-nevsehir-test","error":"Flight search failed: Request failed with status code 500","level":"error","message":"Failed to check flight configuration","service":"flight-monitor","timestamp":"2025-06-07 23:46:36"}
{"level":"info","message":"Flight availability check completed","service":"flight-monitor","timestamp":"2025-06-07 23:46:36"}
{"failedChecks":0,"flightsFound":0,"lastCheckTime":"2025-06-08T03:46:36.030Z","level":"info","message":"✅ Single check completed:","notificationsSent":0,"service":"flight-monitor","successfulChecks":1,"timestamp":"2025-06-07 23:46:36","totalChecks":1,"uptime":0}
{"level":"info","message":"Email service initialized","service":"gmail","timestamp":"2025-06-07 23:47:07"}
{"level":"info","message":"AWS SNS service initialized","service":"flight-monitor","timestamp":"2025-06-07 23:47:07"}
{"error":"Invalid login: 535-5.7.8 Username and Password not accepted. For more information, go to\n535 5.7.8  https://support.google.com/mail/?p=BadCredentials d75a77b69052e-4a61117d70bsm38646881cf.34 - gsmtp","level":"error","message":"Email connection test failed","service":"flight-monitor","timestamp":"2025-06-07 23:47:07"}
{"level":"info","message":"SMS connection test successful","service":"flight-monitor","timestamp":"2025-06-07 23:47:08"}
{"configId":"test-notification","flight":"TK2010","level":"info","message":"SMS notification sent successfully","service":"flight-monitor","timestamp":"2025-06-07 23:47:08","to":"+13052820329"}
{"configId":"test-notification","error":"Invalid login: 535-5.7.8 Username and Password not accepted. For more information, go to\n535 5.7.8  https://support.google.com/mail/?p=BadCredentials d75a77b69052e-4a619852fddsm39451521cf.40 - gsmtp","level":"error","message":"Failed to send email notification","service":"flight-monitor","timestamp":"2025-06-07 23:47:08"}
{"configId":"test-notification","error":"Invalid login: 535-5.7.8 Username and Password not accepted. For more information, go to\n535 5.7.8  https://support.google.com/mail/?p=BadCredentials d75a77b69052e-4a619852fddsm39451521cf.40 - gsmtp","level":"error","message":"Some notifications failed to send","service":"flight-monitor","timestamp":"2025-06-07 23:47:08"}
{"level":"info","message":"Email service initialized","service":"gmail","timestamp":"2025-06-07 23:53:47"}
{"level":"info","message":"AWS SNS service initialized","service":"flight-monitor","timestamp":"2025-06-07 23:53:47"}
{"level":"info","message":"🔍 Running single flight check...","service":"flight-monitor","timestamp":"2025-06-07 23:53:47"}
{"count":2,"level":"info","message":"Flight configurations loaded","service":"flight-monitor","timestamp":"2025-06-07 23:53:47"}
{"activeConfigs":1,"configCount":2,"level":"info","message":"Flight Monitor Service initialized","service":"flight-monitor","timestamp":"2025-06-07 23:53:47"}
{"configCount":1,"level":"info","message":"Starting flight availability check","service":"flight-monitor","timestamp":"2025-06-07 23:53:47"}
{"level":"info","message":"Searching flights","searchRequest":{"cabinClass":"ECONOMY","departureDate":"2024-12-25","destinationCode":"AYT","originCode":"IST","passengers":{"adults":1}},"service":"flight-monitor","timestamp":"2025-06-07 23:53:47"}
{"data":{"data":{},"message":{"code":"TK-BWS-10000","description":"$.OriginDestinationInformation[0].CabinPreferences: is missing and it is not optional, $.OriginDestinationInformation[0].DepartureDateTime.WindowAfter: is missing and it is not optional, $.OriginDestinationInformation[0].DepartureDateTime.WindowBefore: is missing and it is not optional"},"requestId":"0000019442767783-3c844011","status":"FAILURE"},"level":"error","message":"Turkish Airlines API Response Error Request failed with status code 400","service":"flight-monitor","status":400,"statusText":"Bad Request","timestamp":"2025-06-07 23:53:48"}
{"error":"Request failed with status code 400","level":"error","message":"Flight search failed","searchRequest":{"cabinClass":"ECONOMY","departureDate":"2024-12-25","destinationCode":"AYT","originCode":"IST","passengers":{"adults":1}},"service":"flight-monitor","timestamp":"2025-06-07 23:53:48"}
{"configId":"istanbul-nevsehir-test","error":"Flight search failed: Request failed with status code 400","level":"error","message":"Failed to check flight configuration","service":"flight-monitor","timestamp":"2025-06-07 23:53:48"}
{"level":"info","message":"Flight availability check completed","service":"flight-monitor","timestamp":"2025-06-07 23:53:48"}
{"failedChecks":0,"flightsFound":0,"lastCheckTime":"2025-06-08T03:53:47.459Z","level":"info","message":"✅ Single check completed:","notificationsSent":0,"service":"flight-monitor","successfulChecks":1,"timestamp":"2025-06-07 23:53:48","totalChecks":1,"uptime":0}
{"level":"info","message":"Email service initialized","service":"gmail","timestamp":"2025-06-07 23:54:18"}
{"level":"info","message":"AWS SNS service initialized","service":"flight-monitor","timestamp":"2025-06-07 23:54:18"}
{"level":"info","message":"🔍 Running single flight check...","service":"flight-monitor","timestamp":"2025-06-07 23:54:18"}
{"count":2,"level":"info","message":"Flight configurations loaded","service":"flight-monitor","timestamp":"2025-06-07 23:54:18"}
{"activeConfigs":1,"configCount":2,"level":"info","message":"Flight Monitor Service initialized","service":"flight-monitor","timestamp":"2025-06-07 23:54:18"}
{"configCount":1,"level":"info","message":"Starting flight availability check","service":"flight-monitor","timestamp":"2025-06-07 23:54:18"}
{"level":"info","message":"Searching flights","searchRequest":{"cabinClass":"ECONOMY","departureDate":"2024-12-25","destinationCode":"AYT","originCode":"IST","passengers":{"adults":1}},"service":"flight-monitor","timestamp":"2025-06-07 23:54:18"}
{"data":{"data":{},"message":{"code":"TK-TK-BWS-99999","description":"Unknown Error"},"requestId":"0000019442767783-3c844044","status":"FAILURE"},"level":"error","message":"Turkish Airlines API Response Error Request failed with status code 500","service":"flight-monitor","status":500,"statusText":"Internal Server Error","timestamp":"2025-06-07 23:54:19"}
{"error":"Request failed with status code 500","level":"error","message":"Flight search failed","searchRequest":{"cabinClass":"ECONOMY","departureDate":"2024-12-25","destinationCode":"AYT","originCode":"IST","passengers":{"adults":1}},"service":"flight-monitor","timestamp":"2025-06-07 23:54:19"}
{"configId":"istanbul-nevsehir-test","error":"Flight search failed: Request failed with status code 500","level":"error","message":"Failed to check flight configuration","service":"flight-monitor","timestamp":"2025-06-07 23:54:19"}
{"level":"info","message":"Flight availability check completed","service":"flight-monitor","timestamp":"2025-06-07 23:54:19"}
{"failedChecks":0,"flightsFound":0,"lastCheckTime":"2025-06-08T03:54:18.580Z","level":"info","message":"✅ Single check completed:","notificationsSent":0,"service":"flight-monitor","successfulChecks":1,"timestamp":"2025-06-07 23:54:19","totalChecks":1,"uptime":0}
{"level":"info","message":"Email service initialized","service":"gmail","timestamp":"2025-06-07 23:54:46"}
{"level":"info","message":"AWS SNS service initialized","service":"flight-monitor","timestamp":"2025-06-07 23:54:46"}
{"level":"info","message":"🔍 Running single flight check...","service":"flight-monitor","timestamp":"2025-06-07 23:54:46"}
{"count":2,"level":"info","message":"Flight configurations loaded","service":"flight-monitor","timestamp":"2025-06-07 23:54:46"}
{"activeConfigs":1,"configCount":2,"level":"info","message":"Flight Monitor Service initialized","service":"flight-monitor","timestamp":"2025-06-07 23:54:46"}
{"configCount":1,"level":"info","message":"Starting flight availability check","service":"flight-monitor","timestamp":"2025-06-07 23:54:46"}
{"level":"info","message":"Searching flights","searchRequest":{"cabinClass":"ECONOMY","departureDate":"2025-09-19","destinationCode":"NAV","originCode":"IST","passengers":{"adults":1}},"service":"flight-monitor","timestamp":"2025-06-07 23:54:46"}
{"data":{"data":{},"message":{"code":"TK-TK-BWS-99999","description":"Unknown Error"},"requestId":"000001944287d863-3c2984c5","status":"FAILURE"},"level":"error","message":"Turkish Airlines API Response Error Request failed with status code 500","service":"flight-monitor","status":500,"statusText":"Internal Server Error","timestamp":"2025-06-07 23:54:47"}
{"error":"Request failed with status code 500","level":"error","message":"Flight search failed","searchRequest":{"cabinClass":"ECONOMY","departureDate":"2025-09-19","destinationCode":"NAV","originCode":"IST","passengers":{"adults":1}},"service":"flight-monitor","timestamp":"2025-06-07 23:54:47"}
{"configId":"istanbul-nevsehir-test","error":"Flight search failed: Request failed with status code 500","level":"error","message":"Failed to check flight configuration","service":"flight-monitor","timestamp":"2025-06-07 23:54:47"}
{"level":"info","message":"Flight availability check completed","service":"flight-monitor","timestamp":"2025-06-07 23:54:47"}
{"failedChecks":0,"flightsFound":0,"lastCheckTime":"2025-06-08T03:54:46.780Z","level":"info","message":"✅ Single check completed:","notificationsSent":0,"service":"flight-monitor","successfulChecks":1,"timestamp":"2025-06-07 23:54:47","totalChecks":1,"uptime":0}
{"level":"info","message":"Email service initialized","service":"gmail","timestamp":"2025-06-07 23:58:25"}
{"level":"info","message":"AWS SNS service initialized","service":"flight-monitor","timestamp":"2025-06-07 23:58:25"}
{"configId":"istanbul-nevsehir-real","flight":"TK2010","level":"info","message":"SMS notification sent successfully","service":"flight-monitor","timestamp":"2025-06-07 23:58:26","to":"+13052820329"}
{"configId":"istanbul-nevsehir-real","error":"Invalid login: 535-5.7.8 Username and Password not accepted. For more information, go to\n535 5.7.8  https://support.google.com/mail/?p=BadCredentials d75a77b69052e-4a6f82194d9sm5898811cf.79 - gsmtp","level":"error","message":"Failed to send email notification","service":"flight-monitor","timestamp":"2025-06-07 23:58:26"}
{"configId":"istanbul-nevsehir-real","error":"Invalid login: 535-5.7.8 Username and Password not accepted. For more information, go to\n535 5.7.8  https://support.google.com/mail/?p=BadCredentials d75a77b69052e-4a6f82194d9sm5898811cf.79 - gsmtp","level":"error","message":"Some notifications failed to send","service":"flight-monitor","timestamp":"2025-06-07 23:58:26"}
{"level":"info","message":"Email service initialized","service":"aws-ses","timestamp":"2025-06-08 00:00:08"}
{"level":"info","message":"AWS SNS service initialized","service":"flight-monitor","timestamp":"2025-06-08 00:00:08"}
{"configId":"istanbul-nevsehir-real","flight":"TK2010","level":"info","message":"SMS notification sent successfully","service":"flight-monitor","timestamp":"2025-06-08 00:00:08","to":"+13052820329"}
{"configId":"istanbul-nevsehir-real","error":"Email address is not verified. The following identities failed the check in region US-EAST-1: <EMAIL>","level":"error","message":"Failed to send email notification","service":"flight-monitor","timestamp":"2025-06-08 00:00:08"}
{"configId":"istanbul-nevsehir-real","error":"Email address is not verified. The following identities failed the check in region US-EAST-1: <EMAIL>","level":"error","message":"Some notifications failed to send","service":"flight-monitor","timestamp":"2025-06-08 00:00:08"}
{"level":"info","message":"Email notifications disabled","service":"flight-monitor","timestamp":"2025-06-08 00:01:39"}
{"level":"info","message":"AWS SNS service initialized","service":"flight-monitor","timestamp":"2025-06-08 00:01:39"}
{"configId":"istanbul-nevsehir-real","flight":"TK2010","level":"info","message":"SMS notification sent successfully","service":"flight-monitor","timestamp":"2025-06-08 00:01:39","to":"+13052820329"}
{"configId":"istanbul-nevsehir-real","level":"info","message":"All notifications sent successfully","service":"flight-monitor","timestamp":"2025-06-08 00:01:39"}
{"level":"info","message":"Email notifications disabled","service":"flight-monitor","timestamp":"2025-06-08 00:11:52"}
{"level":"info","message":"AWS SNS service initialized","service":"flight-monitor","timestamp":"2025-06-08 00:11:52"}
{"level":"info","message":"🔍 Running single flight check...","service":"flight-monitor","timestamp":"2025-06-08 00:11:52"}
{"count":2,"level":"info","message":"Flight configurations loaded","service":"flight-monitor","timestamp":"2025-06-08 00:11:52"}
{"activeConfigs":1,"configCount":2,"level":"info","message":"Flight Monitor Service initialized","service":"flight-monitor","timestamp":"2025-06-08 00:11:52"}
{"configCount":1,"level":"info","message":"Starting flight availability check","service":"flight-monitor","timestamp":"2025-06-08 00:11:52"}
{"level":"info","message":"Searching flights","searchRequest":{"cabinClass":"ECONOMY","departureDate":"2025-01-15","destinationCode":"ESB","originCode":"IST","passengers":{"adults":1}},"service":"flight-monitor","timestamp":"2025-06-08 00:11:52"}
{"data":{"data":{},"message":{"code":"TK-TK-BWS-99999","description":"Unknown Error"},"requestId":"0000019442767783-3c8447eb","status":"FAILURE"},"level":"error","message":"Turkish Airlines API Response Error Request failed with status code 500","service":"flight-monitor","status":500,"statusText":"Internal Server Error","timestamp":"2025-06-08 00:11:53"}
{"error":"Request failed with status code 500","level":"error","message":"Flight search failed","searchRequest":{"cabinClass":"ECONOMY","departureDate":"2025-01-15","destinationCode":"ESB","originCode":"IST","passengers":{"adults":1}},"service":"flight-monitor","timestamp":"2025-06-08 00:11:53"}
{"error":"Flight search failed: Request failed with status code 500","level":"warn","message":"Turkish Airlines API failed, trying Amadeus","service":"flight-monitor","timestamp":"2025-06-08 00:11:53"}
{"level":"info","message":"Searching flights with Amadeus","searchRequest":{"cabinClass":"ECONOMY","departureDate":"2025-01-15","destinationCode":"ESB","originCode":"IST","passengers":{"adults":1}},"service":"flight-monitor","timestamp":"2025-06-08 00:11:53"}
{"level":"info","message":"Getting Amadeus access token","service":"flight-monitor","timestamp":"2025-06-08 00:11:53"}
{"level":"info","message":"Amadeus access token obtained successfully","service":"flight-monitor","timestamp":"2025-06-08 00:11:54"}
{"data":{"errors":[{"code":425,"detail":"Date/Time is in the past","source":{},"status":400,"title":"INVALID DATE"}]},"level":"error","message":"Amadeus API Response Error Request failed with status code 400","service":"flight-monitor","status":400,"statusText":"Bad Request","timestamp":"2025-06-08 00:11:59"}
{"error":"Request failed with status code 400","level":"error","message":"Amadeus flight search failed","searchRequest":{"cabinClass":"ECONOMY","departureDate":"2025-01-15","destinationCode":"ESB","originCode":"IST","passengers":{"adults":1}},"service":"flight-monitor","timestamp":"2025-06-08 00:11:59"}
{"amadeusError":"Amadeus flight search failed: Request failed with status code 400","level":"error","message":"Both APIs failed","service":"flight-monitor","timestamp":"2025-06-08 00:11:59","turkishError":"Flight search failed: Request failed with status code 500"}
{"configId":"istanbul-nevsehir-test","error":"Both Turkish Airlines and Amadeus APIs failed","level":"error","message":"Failed to check flight configuration","service":"flight-monitor","timestamp":"2025-06-08 00:11:59"}
{"level":"info","message":"Flight availability check completed","service":"flight-monitor","timestamp":"2025-06-08 00:11:59"}
{"failedChecks":0,"flightsFound":0,"lastCheckTime":"2025-06-08T04:11:52.275Z","level":"info","message":"✅ Single check completed:","notificationsSent":0,"service":"flight-monitor","successfulChecks":1,"timestamp":"2025-06-08 00:11:59","totalChecks":1,"uptime":6}
{"level":"info","message":"Email notifications disabled","service":"flight-monitor","timestamp":"2025-06-08 00:12:23"}
{"level":"info","message":"AWS SNS service initialized","service":"flight-monitor","timestamp":"2025-06-08 00:12:23"}
{"level":"info","message":"🔍 Running single flight check...","service":"flight-monitor","timestamp":"2025-06-08 00:12:23"}
{"count":2,"level":"info","message":"Flight configurations loaded","service":"flight-monitor","timestamp":"2025-06-08 00:12:23"}
{"activeConfigs":1,"configCount":2,"level":"info","message":"Flight Monitor Service initialized","service":"flight-monitor","timestamp":"2025-06-08 00:12:23"}
{"configCount":1,"level":"info","message":"Starting flight availability check","service":"flight-monitor","timestamp":"2025-06-08 00:12:23"}
{"level":"info","message":"Searching flights","searchRequest":{"cabinClass":"ECONOMY","departureDate":"2025-09-19","destinationCode":"NAV","originCode":"IST","passengers":{"adults":1}},"service":"flight-monitor","timestamp":"2025-06-08 00:12:23"}
{"data":{"data":{},"message":{"code":"TK-TK-BWS-99999","description":"Unknown Error"},"requestId":"0000019442767783-3c84481f","status":"FAILURE"},"level":"error","message":"Turkish Airlines API Response Error Request failed with status code 500","service":"flight-monitor","status":500,"statusText":"Internal Server Error","timestamp":"2025-06-08 00:12:24"}
{"error":"Request failed with status code 500","level":"error","message":"Flight search failed","searchRequest":{"cabinClass":"ECONOMY","departureDate":"2025-09-19","destinationCode":"NAV","originCode":"IST","passengers":{"adults":1}},"service":"flight-monitor","timestamp":"2025-06-08 00:12:24"}
{"error":"Flight search failed: Request failed with status code 500","level":"warn","message":"Turkish Airlines API failed, trying Amadeus","service":"flight-monitor","timestamp":"2025-06-08 00:12:24"}
{"level":"info","message":"Searching flights with Amadeus","searchRequest":{"cabinClass":"ECONOMY","departureDate":"2025-09-19","destinationCode":"NAV","originCode":"IST","passengers":{"adults":1}},"service":"flight-monitor","timestamp":"2025-06-08 00:12:24"}
{"level":"info","message":"Getting Amadeus access token","service":"flight-monitor","timestamp":"2025-06-08 00:12:24"}
{"level":"info","message":"Amadeus access token obtained successfully","service":"flight-monitor","timestamp":"2025-06-08 00:12:25"}
{"flightCount":7,"level":"info","message":"Amadeus flight search completed","service":"flight-monitor","timestamp":"2025-06-08 00:12:26"}
{"apiUsed":"Amadeus","configId":"istanbul-nevsehir-test","level":"info","message":"Flight search successful","service":"flight-monitor","timestamp":"2025-06-08 00:12:26"}
{"id":"istanbul-nevsehir-test","level":"info","message":"Flight configuration updated","service":"flight-monitor","timestamp":"2025-06-08 00:12:26","updates":{"lastChecked":"2025-06-08T04:12:26.248Z"}}
{"level":"info","message":"Flight availability check completed","service":"flight-monitor","timestamp":"2025-06-08 00:12:26"}
{"failedChecks":0,"flightsFound":0,"lastCheckTime":"2025-06-08T04:12:23.455Z","level":"info","message":"✅ Single check completed:","notificationsSent":0,"service":"flight-monitor","successfulChecks":1,"timestamp":"2025-06-08 00:12:26","totalChecks":1,"uptime":2}
{"level":"info","message":"Getting Amadeus access token","service":"flight-monitor","timestamp":"2025-06-08 00:12:58"}
{"level":"info","message":"Amadeus access token obtained successfully","service":"flight-monitor","timestamp":"2025-06-08 00:12:59"}
{"level":"info","message":"Amadeus API connection test successful","service":"flight-monitor","timestamp":"2025-06-08 00:12:59"}
{"level":"info","message":"Searching flights with Amadeus","searchRequest":{"cabinClass":"ECONOMY","departureDate":"2025-09-19","destinationCode":"NAV","originCode":"IST","passengers":{"adults":1}},"service":"flight-monitor","timestamp":"2025-06-08 00:12:59"}
{"flightCount":7,"level":"info","message":"Amadeus flight search completed","service":"flight-monitor","timestamp":"2025-06-08 00:13:00"}
