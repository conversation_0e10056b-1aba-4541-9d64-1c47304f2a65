import { ApiConfig, NotificationConfig } from '../types/flight.types.js';
import dotenv from 'dotenv';

dotenv.config();

export const apiConfig: ApiConfig = {
  apiKey: process.env.TK_API_KEY || '',
  apiSecret: process.env.TK_API_SECRET || '',
  baseUrl: process.env.TK_API_BASE_URL || 'https://api.turkishairlines.com',
  timeout: 30000, // 30 seconds
  retryAttempts: 3
};

export const notificationConfig: NotificationConfig = {
  email: {
    enabled: process.env.ENABLE_EMAIL_NOTIFICATIONS === 'true',
    service: (process.env.EMAIL_SERVICE as 'gmail' | 'aws-ses' | 'smtp') || 'gmail',
    from: process.env.SES_FROM_EMAIL || process.env.GMAIL_USER || '',
    to: process.env.NOTIFICATION_EMAIL || '',
    smtp: {
      host: process.env.SMTP_HOST || 'smtp.gmail.com',
      port: parseInt(process.env.SMTP_PORT || '587'),
      secure: process.env.SMTP_SECURE === 'true',
      auth: {
        user: process.env.GMAIL_USER || '',
        pass: process.env.GMAIL_APP_PASSWORD || ''
      }
    }
  },
  sms: {
    enabled: process.env.ENABLE_SMS_NOTIFICATIONS === 'true',
    service: (process.env.SMS_SERVICE as 'aws-sns' | 'twilio') || 'aws-sns',
    to: process.env.NOTIFICATION_PHONE || '',
    aws: {
      region: process.env.AWS_REGION || 'us-east-1',
      topicArn: process.env.SNS_TOPIC_ARN || ''
    },
    twilio: {
      accountSid: process.env.TWILIO_ACCOUNT_SID || '',
      authToken: process.env.TWILIO_AUTH_TOKEN || '',
      from: process.env.TWILIO_FROM_PHONE || ''
    }
  }
};

export const monitoringConfig = {
  checkIntervalMinutes: parseInt(process.env.CHECK_INTERVAL_MINUTES || '30'),
  maxRetries: 3,
  retryDelayMs: 5000,
  requestTimeoutMs: 30000
};

// Validation function
export function validateConfig(): { valid: boolean; errors: string[] } {
  const errors: string[] = [];

  // Validate Turkish Airlines API config
  if (!apiConfig.apiKey) {
    errors.push('TK_API_KEY is required');
  }
  // Note: TK_API_SECRET is optional for some Turkish Airlines API configurations

  // Validate notification config
  if (notificationConfig.email.enabled) {
    if (!notificationConfig.email.to) {
      errors.push('NOTIFICATION_EMAIL is required when email notifications are enabled');
    }
    if (notificationConfig.email.service === 'gmail' && !notificationConfig.email.smtp?.auth.pass) {
      errors.push('GMAIL_APP_PASSWORD is required when using Gmail service');
    }
  }

  if (notificationConfig.sms.enabled) {
    if (!notificationConfig.sms.to) {
      errors.push('NOTIFICATION_PHONE is required when SMS notifications are enabled');
    }
    if (notificationConfig.sms.service === 'aws-sns' && !notificationConfig.sms.aws?.topicArn) {
      errors.push('SNS_TOPIC_ARN is required when using AWS SNS service');
    }
    if (notificationConfig.sms.service === 'twilio' && (!notificationConfig.sms.twilio?.accountSid || !notificationConfig.sms.twilio?.authToken)) {
      errors.push('TWILIO_ACCOUNT_SID and TWILIO_AUTH_TOKEN are required when using Twilio service');
    }
  }

  return {
    valid: errors.length === 0,
    errors
  };
}
