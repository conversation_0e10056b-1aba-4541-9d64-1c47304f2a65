import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';
import cron from 'node-cron';
import { TurkishAirlinesService } from './turkishAirlinesService.js';
import { NotificationService } from './notificationService.js';
import { monitoringConfig } from '../config/apiConfig.js';
import logger from '../utils/logger.js';
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
export class FlightMonitorService {
    turkishAirlines;
    notifications;
    configs = [];
    configsPath;
    cronJob = null;
    stats;
    startTime;
    constructor() {
        this.turkishAirlines = new TurkishAirlinesService();
        this.notifications = new NotificationService();
        this.configsPath = path.join(__dirname, '../config/flight-configs.json');
        this.startTime = new Date();
        this.stats = {
            totalChecks: 0,
            successfulChecks: 0,
            failedChecks: 0,
            flightsFound: 0,
            notificationsSent: 0,
            lastCheckTime: '',
            uptime: 0
        };
    }
    async initialize() {
        try {
            await this.loadConfigurations();
            logger.info('Flight Monitor Service initialized', {
                configCount: this.configs.length,
                activeConfigs: this.configs.filter(c => c.active).length
            });
        }
        catch (error) {
            logger.error('Failed to initialize Flight Monitor Service', { error: error instanceof Error ? error.message : String(error) });
            throw error;
        }
    }
    async loadConfigurations() {
        try {
            const configData = await fs.readFile(this.configsPath, 'utf-8');
            this.configs = JSON.parse(configData);
            logger.info('Flight configurations loaded', { count: this.configs.length });
        }
        catch (error) {
            if (error.code === 'ENOENT') {
                logger.warn('Flight configurations file not found, creating empty configuration');
                this.configs = [];
                await this.saveConfigurations();
            }
            else {
                logger.error('Failed to load flight configurations', { error: error instanceof Error ? error.message : String(error) });
                throw error;
            }
        }
    }
    async saveConfigurations() {
        try {
            await fs.writeFile(this.configsPath, JSON.stringify(this.configs, null, 2));
            logger.debug('Flight configurations saved');
        }
        catch (error) {
            logger.error('Failed to save flight configurations', { error: error instanceof Error ? error.message : String(error) });
            throw error;
        }
    }
    async addConfiguration(config) {
        const newConfig = {
            ...config,
            id: `config-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
            createdAt: new Date().toISOString()
        };
        this.configs.push(newConfig);
        await this.saveConfigurations();
        logger.info('Flight configuration added', {
            id: newConfig.id,
            name: newConfig.name,
            route: `${newConfig.search.originCode} → ${newConfig.search.destinationCode}`
        });
        return newConfig.id;
    }
    async removeConfiguration(id) {
        const initialLength = this.configs.length;
        this.configs = this.configs.filter(config => config.id !== id);
        if (this.configs.length < initialLength) {
            await this.saveConfigurations();
            logger.info('Flight configuration removed', { id });
            return true;
        }
        return false;
    }
    async updateConfiguration(id, updates) {
        const configIndex = this.configs.findIndex(config => config.id === id);
        if (configIndex === -1) {
            return false;
        }
        this.configs[configIndex] = { ...this.configs[configIndex], ...updates };
        await this.saveConfigurations();
        logger.info('Flight configuration updated', { id, updates });
        return true;
    }
    getConfigurations() {
        return [...this.configs];
    }
    getConfiguration(id) {
        return this.configs.find(config => config.id === id);
    }
    async checkAllFlights() {
        const activeConfigs = this.configs.filter(config => config.active);
        if (activeConfigs.length === 0) {
            logger.info('No active flight configurations to check');
            return;
        }
        logger.info('Starting flight availability check', { configCount: activeConfigs.length });
        this.stats.totalChecks++;
        this.stats.lastCheckTime = new Date().toISOString();
        const checkPromises = activeConfigs.map(config => this.checkFlightConfig(config));
        try {
            await Promise.allSettled(checkPromises);
            this.stats.successfulChecks++;
            logger.info('Flight availability check completed');
        }
        catch (error) {
            this.stats.failedChecks++;
            logger.error('Flight availability check failed', { error: error instanceof Error ? error.message : String(error) });
        }
    }
    async checkFlightConfig(config) {
        try {
            logger.debug('Checking flight configuration', {
                id: config.id,
                name: config.name,
                route: `${config.search.originCode} → ${config.search.destinationCode}`
            });
            const searchResult = await this.turkishAirlines.searchFlights(config.search);
            // Filter flights based on target criteria
            const matchingFlights = this.filterMatchingFlights(searchResult.flights, config);
            if (matchingFlights.length > 0) {
                this.stats.flightsFound += matchingFlights.length;
                logger.info('Matching flights found', {
                    configId: config.id,
                    flightCount: matchingFlights.length,
                    flights: matchingFlights.map(f => f.segments[0].flightNumber)
                });
                // Send notifications for each matching flight
                for (const flight of matchingFlights) {
                    await this.sendFlightNotification(config, flight);
                }
                // Update last available time
                await this.updateConfiguration(config.id, {
                    lastAvailable: new Date().toISOString()
                });
            }
            else {
                logger.debug('No matching flights found', { configId: config.id });
            }
            // Update last checked time
            await this.updateConfiguration(config.id, {
                lastChecked: new Date().toISOString()
            });
        }
        catch (error) {
            logger.error('Failed to check flight configuration', {
                configId: config.id,
                error: error instanceof Error ? error.message : String(error)
            });
        }
    }
    filterMatchingFlights(flights, config) {
        return flights.filter(flight => {
            // Only consider available flights
            if (!flight.available) {
                return false;
            }
            const mainSegment = flight.segments[0];
            // Check target flight numbers
            if (config.targetFlights.length > 0) {
                const flightMatches = config.targetFlights.some(targetFlight => mainSegment.flightNumber === targetFlight);
                if (!flightMatches) {
                    return false;
                }
            }
            // Check target times
            if (config.targetTimes && config.targetTimes.length > 0) {
                const departureTime = new Date(mainSegment.departureDateTime);
                const timeString = departureTime.toTimeString().substring(0, 5); // HH:MM format
                const timeMatches = config.targetTimes.some(targetTime => timeString === targetTime);
                if (!timeMatches) {
                    return false;
                }
            }
            return true;
        });
    }
    async sendFlightNotification(config, flight) {
        const notificationData = {
            configId: config.id,
            configName: config.name,
            flight,
            message: `Flight ${flight.segments[0].flightNumber} is now available!`,
            timestamp: new Date().toISOString()
        };
        try {
            await this.notifications.sendFlightAvailableNotification(notificationData);
            this.stats.notificationsSent++;
            logger.info('Flight notification sent', {
                configId: config.id,
                flight: flight.segments[0].flightNumber
            });
        }
        catch (error) {
            logger.error('Failed to send flight notification', {
                configId: config.id,
                flight: flight.segments[0].flightNumber,
                error: error instanceof Error ? error.message : String(error)
            });
        }
    }
    startMonitoring() {
        if (this.cronJob) {
            logger.warn('Monitoring is already running');
            return;
        }
        const cronExpression = `*/${monitoringConfig.checkIntervalMinutes} * * * *`;
        this.cronJob = cron.schedule(cronExpression, async () => {
            await this.checkAllFlights();
        }, {
            scheduled: false
        });
        this.cronJob.start();
        logger.info('Flight monitoring started', {
            intervalMinutes: monitoringConfig.checkIntervalMinutes,
            cronExpression
        });
    }
    stopMonitoring() {
        if (this.cronJob) {
            this.cronJob.stop();
            this.cronJob = null;
            logger.info('Flight monitoring stopped');
        }
    }
    getStats() {
        return {
            ...this.stats,
            uptime: Math.floor((Date.now() - this.startTime.getTime()) / 1000)
        };
    }
    async testServices() {
        const results = {
            api: false,
            email: false,
            sms: false
        };
        try {
            results.api = await this.turkishAirlines.testConnection();
        }
        catch (error) {
            logger.error('API test failed', { error: error instanceof Error ? error.message : String(error) });
        }
        try {
            results.email = await this.notifications.testEmailConnection();
        }
        catch (error) {
            logger.error('Email test failed', { error: error instanceof Error ? error.message : String(error) });
        }
        try {
            results.sms = await this.notifications.testSMSConnection();
        }
        catch (error) {
            logger.error('SMS test failed', { error: error instanceof Error ? error.message : String(error) });
        }
        return results;
    }
}
