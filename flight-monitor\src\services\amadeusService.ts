import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { 
  FlightSearchRequest, 
  FlightSearchResponse, 
  FlightOption,
  FlightSegment
} from '../types/flight.types.js';
import logger from '../utils/logger.js';

interface AmadeusConfig {
  apiKey: string;
  apiSecret: string;
  baseUrl: string;
}

interface AmadeusTokenResponse {
  access_token: string;
  token_type: string;
  expires_in: number;
}

interface AmadeusFlightResponse {
  data: Array<{
    id: string;
    source: string;
    instantTicketingRequired: boolean;
    nonHomogeneous: boolean;
    oneWay: boolean;
    lastTicketingDate: string;
    numberOfBookableSeats: number;
    itineraries: Array<{
      duration: string;
      segments: Array<{
        departure: {
          iataCode: string;
          terminal?: string;
          at: string;
        };
        arrival: {
          iataCode: string;
          terminal?: string;
          at: string;
        };
        carrierCode: string;
        number: string;
        aircraft: {
          code: string;
        };
        operating?: {
          carrierCode: string;
        };
        duration: string;
        id: string;
        numberOfStops: number;
        blacklistedInEU: boolean;
      }>;
    }>;
    price: {
      currency: string;
      total: string;
      base: string;
      fees: Array<{
        amount: string;
        type: string;
      }>;
      grandTotal: string;
    };
    pricingOptions: {
      fareType: string[];
      includedCheckedBagsOnly: boolean;
    };
    validatingAirlineCodes: string[];
    travelerPricings: Array<{
      travelerId: string;
      fareOption: string;
      travelerType: string;
      price: {
        currency: string;
        total: string;
        base: string;
      };
      fareDetailsBySegment: Array<{
        segmentId: string;
        cabin: string;
        fareBasis: string;
        brandedFare?: string;
        class: string;
        includedCheckedBags: {
          quantity: number;
        };
      }>;
    }>;
  }>;
  meta: {
    count: number;
    links: {
      self: string;
    };
  };
}

export class AmadeusService {
  private client: AxiosInstance;
  private config: AmadeusConfig;
  private accessToken: string | null = null;
  private tokenExpiry: Date | null = null;

  constructor() {
    this.config = {
      apiKey: process.env.AMADEUS_API_KEY || '',
      apiSecret: process.env.AMADEUS_API_SECRET || '',
      baseUrl: process.env.AMADEUS_API_BASE_URL || 'https://test.api.amadeus.com'
    };

    this.client = axios.create({
      baseURL: this.config.baseUrl,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    });

    // Add request interceptor for logging
    this.client.interceptors.request.use(
      (config) => {
        logger.debug('Amadeus API Request', {
          url: config.url,
          method: config.method
        });
        return config;
      },
      (error) => {
        logger.error('Amadeus API Request Error', error);
        return Promise.reject(error);
      }
    );

    // Add response interceptor for logging
    this.client.interceptors.response.use(
      (response) => {
        logger.debug('Amadeus API Response', {
          status: response.status,
          statusText: response.statusText
        });
        return response;
      },
      (error) => {
        logger.error('Amadeus API Response Error', {
          status: error.response?.status,
          statusText: error.response?.statusText,
          data: error.response?.data,
          message: error.message
        });
        return Promise.reject(error);
      }
    );
  }

  private async getAccessToken(): Promise<string> {
    // Check if we have a valid token
    if (this.accessToken && this.tokenExpiry && new Date() < this.tokenExpiry) {
      return this.accessToken;
    }

    try {
      logger.info('Getting Amadeus access token');

      const response: AxiosResponse<AmadeusTokenResponse> = await this.client.post(
        '/v1/security/oauth2/token',
        `grant_type=client_credentials&client_id=${this.config.apiKey}&client_secret=${this.config.apiSecret}`
      );

      this.accessToken = response.data.access_token;
      // Set expiry to 5 minutes before actual expiry for safety
      this.tokenExpiry = new Date(Date.now() + (response.data.expires_in - 300) * 1000);

      logger.info('Amadeus access token obtained successfully');
      return this.accessToken;

    } catch (error) {
      logger.error('Failed to get Amadeus access token', { 
        error: error instanceof Error ? error.message : String(error) 
      });
      throw new Error(`Failed to authenticate with Amadeus API: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  async searchFlights(searchRequest: FlightSearchRequest): Promise<FlightSearchResponse> {
    try {
      logger.info('Searching flights with Amadeus', { searchRequest });

      const token = await this.getAccessToken();

      // Update client headers with bearer token
      this.client.defaults.headers.common['Authorization'] = `Bearer ${token}`;
      this.client.defaults.headers.common['Content-Type'] = 'application/json';

      const params = this.buildSearchParams(searchRequest);
      
      const response: AxiosResponse<AmadeusFlightResponse> = await this.client.get(
        '/v2/shopping/flight-offers',
        { params }
      );

      const flights = this.parseFlightResponse(response.data);
      
      logger.info('Amadeus flight search completed', { 
        flightCount: flights.length
      });

      return {
        flights,
        searchId: `amadeus-${Date.now()}`,
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logger.error('Amadeus flight search failed', { error: errorMessage, searchRequest });
      throw new Error(`Amadeus flight search failed: ${errorMessage}`);
    }
  }

  private buildSearchParams(searchRequest: FlightSearchRequest) {
    return {
      originLocationCode: searchRequest.originCode,
      destinationLocationCode: searchRequest.destinationCode,
      departureDate: searchRequest.departureDate,
      adults: searchRequest.passengers.adults,
      children: searchRequest.passengers.children || 0,
      infants: searchRequest.passengers.infants || 0,
      travelClass: searchRequest.cabinClass || 'ECONOMY',
      nonStop: 'false',
      max: 50
    };
  }

  private parseFlightResponse(apiResponse: AmadeusFlightResponse): FlightOption[] {
    const flights: FlightOption[] = [];

    try {
      for (const offer of apiResponse.data) {
        for (const itinerary of offer.itineraries) {
          const segments: FlightSegment[] = itinerary.segments.map(segment => ({
            flightNumber: `${segment.carrierCode}${segment.number}`,
            departureAirport: segment.departure.iataCode,
            arrivalAirport: segment.arrival.iataCode,
            departureDateTime: segment.departure.at,
            arrivalDateTime: segment.arrival.at,
            duration: segment.duration,
            aircraft: segment.aircraft.code,
            operatingAirline: segment.operating?.carrierCode || segment.carrierCode,
            bookingClasses: [{
              code: 'Y',
              availableSeats: offer.numberOfBookableSeats.toString(),
              status: 'A'
            }]
          }));

          flights.push({
            segments,
            totalDuration: itinerary.duration,
            stops: segments.length - 1,
            price: {
              amount: parseFloat(offer.price.total),
              currency: offer.price.currency
            },
            available: offer.numberOfBookableSeats > 0
          });
        }
      }

      return flights;
    } catch (error) {
      logger.error('Error parsing Amadeus flight response', { 
        error: error instanceof Error ? error.message : String(error) 
      });
      return [];
    }
  }

  async testConnection(): Promise<boolean> {
    try {
      await this.getAccessToken();
      logger.info('Amadeus API connection test successful');
      return true;
    } catch (error) {
      logger.error('Amadeus API connection test failed', { 
        error: error instanceof Error ? error.message : String(error) 
      });
      return false;
    }
  }
}
