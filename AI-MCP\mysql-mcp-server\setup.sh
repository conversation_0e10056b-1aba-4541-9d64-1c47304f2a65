#!/bin/bash

# MySQL MCP Server Setup Script

echo "🚀 Setting up MySQL MCP Server..."

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 18+ first."
    exit 1
fi

# Check Node.js version
NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    echo "❌ Node.js version 18+ is required. Current version: $(node -v)"
    exit 1
fi

echo "✅ Node.js $(node -v) detected"

# Install dependencies
echo "📦 Installing dependencies..."
npm install

if [ $? -ne 0 ]; then
    echo "❌ Failed to install dependencies"
    exit 1
fi

echo "✅ Dependencies installed"

# Build the project
echo "🔨 Building TypeScript..."
npm run build

if [ $? -ne 0 ]; then
    echo "❌ Build failed"
    exit 1
fi

echo "✅ Build completed"

# Test the server
echo "🧪 Testing server..."
node test-server.js

if [ $? -ne 0 ]; then
    echo "❌ Server test failed"
    exit 1
fi

echo "✅ Server test passed"

# Create config file if it doesn't exist
if [ ! -f "config.json" ]; then
    echo "📝 Creating config.json from example..."
    cp config.example.json config.json
    echo "⚠️  Please edit config.json with your MySQL credentials"
fi

echo ""
echo "🎉 MySQL MCP Server setup complete!"
echo ""
echo "Next steps:"
echo "1. Edit config.json with your MySQL database credentials"
echo "2. Run the server with: npm start"
echo "3. Or run in development mode with: npm run dev"
echo ""
echo "For usage examples, see example-usage.md"
