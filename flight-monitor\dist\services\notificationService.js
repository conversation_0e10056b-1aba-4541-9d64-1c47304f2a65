import nodemailer from 'nodemailer';
import A<PERSON> from 'aws-sdk';
import { notificationConfig } from '../config/apiConfig.js';
import logger from '../utils/logger.js';
export class NotificationService {
    emailTransporter = null;
    sns = null;
    constructor() {
        this.initializeEmailService();
        this.initializeSMSService();
    }
    initializeEmailService() {
        if (!notificationConfig.email.enabled) {
            logger.info('Email notifications disabled');
            return;
        }
        try {
            if (notificationConfig.email.service === 'gmail') {
                this.emailTransporter = nodemailer.createTransport({
                    service: 'gmail',
                    auth: {
                        user: notificationConfig.email.smtp?.auth.user,
                        pass: notificationConfig.email.smtp?.auth.pass
                    }
                });
            }
            else if (notificationConfig.email.service === 'aws-ses') {
                // Configure AWS SES
                AWS.config.update({
                    region: process.env.AWS_REGION || 'us-east-1',
                    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
                    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY
                });
                this.emailTransporter = nodemailer.createTransport({
                    SES: new AWS.SES({ apiVersion: '2010-12-01' })
                });
            }
            else {
                // Generic SMTP
                this.emailTransporter = nodemailer.createTransport({
                    host: notificationConfig.email.smtp?.host,
                    port: notificationConfig.email.smtp?.port,
                    secure: notificationConfig.email.smtp?.secure,
                    auth: {
                        user: notificationConfig.email.smtp?.auth.user,
                        pass: notificationConfig.email.smtp?.auth.pass
                    }
                });
            }
            logger.info('Email service initialized', { service: notificationConfig.email.service });
        }
        catch (error) {
            logger.error('Failed to initialize email service', { error: error instanceof Error ? error.message : String(error) });
        }
    }
    initializeSMSService() {
        if (!notificationConfig.sms.enabled) {
            logger.info('SMS notifications disabled');
            return;
        }
        try {
            if (notificationConfig.sms.service === 'aws-sns') {
                AWS.config.update({
                    region: notificationConfig.sms.aws?.region || 'us-east-1',
                    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
                    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY
                });
                this.sns = new AWS.SNS();
                logger.info('AWS SNS service initialized');
            }
            // Twilio initialization would go here if needed
        }
        catch (error) {
            logger.error('Failed to initialize SMS service', { error: error instanceof Error ? error.message : String(error) });
        }
    }
    async sendFlightAvailableNotification(data) {
        const promises = [];
        if (notificationConfig.email.enabled) {
            promises.push(this.sendEmailNotification(data));
        }
        if (notificationConfig.sms.enabled) {
            promises.push(this.sendSMSNotification(data));
        }
        try {
            await Promise.all(promises);
            logger.info('All notifications sent successfully', { configId: data.configId });
        }
        catch (error) {
            logger.error('Some notifications failed to send', { error: error instanceof Error ? error.message : String(error), configId: data.configId });
        }
    }
    async sendEmailNotification(data) {
        if (!this.emailTransporter) {
            throw new Error('Email transporter not initialized');
        }
        const flight = data.flight;
        const mainSegment = flight.segments[0];
        const subject = `✈️ Flight Available: ${mainSegment.flightNumber} - ${data.configName}`;
        const htmlContent = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #d32f2f;">🎉 Flight Available!</h2>
        
        <div style="background-color: #f5f5f5; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3 style="margin-top: 0; color: #1976d2;">Flight Details</h3>
          <p><strong>Flight:</strong> ${mainSegment.flightNumber}</p>
          <p><strong>Route:</strong> ${mainSegment.departureAirport} → ${mainSegment.arrivalAirport}</p>
          <p><strong>Departure:</strong> ${new Date(mainSegment.departureDateTime).toLocaleString()}</p>
          <p><strong>Arrival:</strong> ${new Date(mainSegment.arrivalDateTime).toLocaleString()}</p>
          <p><strong>Duration:</strong> ${flight.totalDuration}</p>
          <p><strong>Stops:</strong> ${flight.stops}</p>
        </div>

        <div style="background-color: #e8f5e8; padding: 15px; border-radius: 8px; margin: 20px 0;">
          <p style="margin: 0; color: #2e7d32;"><strong>✅ This flight is now available for booking!</strong></p>
        </div>

        <div style="margin: 20px 0;">
          <p><strong>Monitor Configuration:</strong> ${data.configName}</p>
          <p><strong>Checked at:</strong> ${new Date(data.timestamp).toLocaleString()}</p>
        </div>

        <div style="background-color: #fff3e0; padding: 15px; border-radius: 8px; margin: 20px 0;">
          <p style="margin: 0; color: #f57c00;"><strong>⚡ Act quickly!</strong> Flight availability can change rapidly.</p>
        </div>

        <hr style="margin: 30px 0;">
        <p style="color: #666; font-size: 12px;">
          This notification was sent by Turkish Airlines Flight Monitor.<br>
          Monitor ID: ${data.configId}
        </p>
      </div>
    `;
        const textContent = `
Flight Available: ${mainSegment.flightNumber}

Flight Details:
- Flight: ${mainSegment.flightNumber}
- Route: ${mainSegment.departureAirport} → ${mainSegment.arrivalAirport}
- Departure: ${new Date(mainSegment.departureDateTime).toLocaleString()}
- Arrival: ${new Date(mainSegment.arrivalDateTime).toLocaleString()}
- Duration: ${flight.totalDuration}
- Stops: ${flight.stops}

This flight is now available for booking!

Monitor: ${data.configName}
Checked at: ${new Date(data.timestamp).toLocaleString()}
Monitor ID: ${data.configId}
    `;
        try {
            await this.emailTransporter.sendMail({
                from: notificationConfig.email.from,
                to: notificationConfig.email.to,
                subject,
                text: textContent,
                html: htmlContent
            });
            logger.info('Email notification sent successfully', {
                to: notificationConfig.email.to,
                flight: mainSegment.flightNumber,
                configId: data.configId
            });
        }
        catch (error) {
            logger.error('Failed to send email notification', {
                error: error instanceof Error ? error.message : String(error),
                configId: data.configId
            });
            throw error;
        }
    }
    async sendSMSNotification(data) {
        const flight = data.flight;
        const mainSegment = flight.segments[0];
        const message = `🛫 FLIGHT AVAILABLE!\n\n${mainSegment.flightNumber}\n${mainSegment.departureAirport}→${mainSegment.arrivalAirport}\n${new Date(mainSegment.departureDateTime).toLocaleDateString()} ${new Date(mainSegment.departureDateTime).toLocaleTimeString()}\n\nBook now! - Flight Monitor`;
        try {
            if (notificationConfig.sms.service === 'aws-sns' && this.sns) {
                await this.sendSNSMessage(message);
            }
            else if (notificationConfig.sms.service === 'twilio') {
                await this.sendTwilioMessage(message);
            }
            logger.info('SMS notification sent successfully', {
                to: notificationConfig.sms.to,
                flight: mainSegment.flightNumber,
                configId: data.configId
            });
        }
        catch (error) {
            logger.error('Failed to send SMS notification', {
                error: error instanceof Error ? error.message : String(error),
                configId: data.configId
            });
            throw error;
        }
    }
    async sendSNSMessage(message) {
        if (!this.sns) {
            throw new Error('SNS not initialized');
        }
        // Send to SNS topic (which will notify all subscribers including email and SMS)
        const params = {
            Message: message,
            TopicArn: notificationConfig.sms.aws?.topicArn
        };
        await this.sns.publish(params).promise();
    }
    async sendTwilioMessage(message) {
        // Twilio implementation would go here
        // For now, we'll just log that it would be sent
        logger.info('Twilio SMS would be sent', { message });
        throw new Error('Twilio SMS not implemented yet');
    }
    async testEmailConnection() {
        if (!this.emailTransporter) {
            return false;
        }
        try {
            await this.emailTransporter.verify();
            logger.info('Email connection test successful');
            return true;
        }
        catch (error) {
            logger.error('Email connection test failed', { error: error instanceof Error ? error.message : String(error) });
            return false;
        }
    }
    async testSMSConnection() {
        if (!notificationConfig.sms.enabled) {
            return true; // Not enabled, so consider it "working"
        }
        try {
            if (notificationConfig.sms.service === 'aws-sns' && this.sns) {
                // Test SNS connection by listing topics
                await this.sns.listTopics().promise();
                logger.info('SMS connection test successful');
                return true;
            }
            return true;
        }
        catch (error) {
            logger.error('SMS connection test failed', { error: error instanceof Error ? error.message : String(error) });
            return false;
        }
    }
}
