#!/usr/bin/env node
import { FlightMonitorService } from './services/flightMonitorService.js';
import { validateConfig } from './config/apiConfig.js';
import logger from './utils/logger.js';
import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
class FlightMonitorApp {
    monitorService;
    constructor() {
        this.monitorService = new FlightMonitorService();
    }
    async start() {
        try {
            logger.info('🚀 Starting Turkish Airlines Flight Monitor...');
            // Validate configuration
            const configValidation = validateConfig();
            if (!configValidation.valid) {
                logger.error('Configuration validation failed:', { errors: configValidation.errors });
                process.exit(1);
            }
            // Create logs directory if it doesn't exist
            await this.ensureLogsDirectory();
            // Initialize the monitor service
            await this.monitorService.initialize();
            // Test all services
            logger.info('🧪 Testing services...');
            const testResults = await this.monitorService.testServices();
            logger.info('Service test results:', testResults);
            if (!testResults.api) {
                logger.error('❌ Turkish Airlines API test failed. Please check your API credentials.');
                process.exit(1);
            }
            if (!testResults.email && process.env.ENABLE_EMAIL_NOTIFICATIONS === 'true') {
                logger.warn('⚠️ Email service test failed. Email notifications may not work.');
            }
            if (!testResults.sms && process.env.ENABLE_SMS_NOTIFICATIONS === 'true') {
                logger.warn('⚠️ SMS service test failed. SMS notifications may not work.');
            }
            // Add default configuration if none exist
            await this.ensureDefaultConfiguration();
            // Start monitoring
            this.monitorService.startMonitoring();
            // Run initial check
            logger.info('🔍 Running initial flight check...');
            await this.monitorService.checkAllFlights();
            logger.info('✅ Flight Monitor is now running!');
            this.logStatus();
            // Set up graceful shutdown
            this.setupGracefulShutdown();
            // Log stats periodically
            this.startStatsLogging();
        }
        catch (error) {
            logger.error('Failed to start Flight Monitor:', { error: error.message });
            process.exit(1);
        }
    }
    async ensureLogsDirectory() {
        const logsDir = path.join(__dirname, '../logs');
        try {
            await fs.access(logsDir);
        }
        catch {
            await fs.mkdir(logsDir, { recursive: true });
            logger.info('Created logs directory');
        }
    }
    async ensureDefaultConfiguration() {
        const configs = this.monitorService.getConfigurations();
        if (configs.length === 0) {
            logger.info('No flight configurations found, adding default configuration...');
            const defaultConfig = {
                name: 'Istanbul to Nevsehir - September 19, 2025',
                search: {
                    originCode: 'IST',
                    destinationCode: 'NAV',
                    departureDate: '2025-09-19',
                    passengers: {
                        adults: 1
                    },
                    cabinClass: 'ECONOMY'
                },
                targetFlights: ['TK2010', 'TK2012'],
                targetTimes: ['11:15', '13:30'],
                notifications: {
                    email: true,
                    sms: true
                },
                active: true
            };
            const configId = await this.monitorService.addConfiguration(defaultConfig);
            logger.info('Default configuration added', { configId });
        }
    }
    logStatus() {
        const configs = this.monitorService.getConfigurations();
        const activeConfigs = configs.filter(c => c.active);
        logger.info('📊 Current Status:', {
            totalConfigurations: configs.length,
            activeConfigurations: activeConfigs.length,
            checkInterval: `${process.env.CHECK_INTERVAL_MINUTES || 30} minutes`,
            emailNotifications: process.env.ENABLE_EMAIL_NOTIFICATIONS === 'true',
            smsNotifications: process.env.ENABLE_SMS_NOTIFICATIONS === 'true'
        });
        if (activeConfigs.length > 0) {
            logger.info('📋 Active Monitoring Configurations:');
            activeConfigs.forEach(config => {
                logger.info(`  • ${config.name}`, {
                    route: `${config.search.originCode} → ${config.search.destinationCode}`,
                    date: config.search.departureDate,
                    targetFlights: config.targetFlights,
                    targetTimes: config.targetTimes
                });
            });
        }
    }
    setupGracefulShutdown() {
        const shutdown = (signal) => {
            logger.info(`Received ${signal}, shutting down gracefully...`);
            this.monitorService.stopMonitoring();
            logger.info('Flight Monitor stopped');
            process.exit(0);
        };
        process.on('SIGTERM', () => shutdown('SIGTERM'));
        process.on('SIGINT', () => shutdown('SIGINT'));
        process.on('uncaughtException', (error) => {
            logger.error('Uncaught Exception:', { error: error.message, stack: error.stack });
            process.exit(1);
        });
        process.on('unhandledRejection', (reason, promise) => {
            logger.error('Unhandled Rejection:', { reason, promise });
            process.exit(1);
        });
    }
    startStatsLogging() {
        // Log stats every hour
        setInterval(() => {
            const stats = this.monitorService.getStats();
            logger.info('📈 Monitoring Statistics:', stats);
        }, 60 * 60 * 1000); // 1 hour
    }
    async runSingleCheck() {
        try {
            logger.info('🔍 Running single flight check...');
            await this.monitorService.initialize();
            await this.monitorService.checkAllFlights();
            const stats = this.monitorService.getStats();
            logger.info('✅ Single check completed:', stats);
        }
        catch (error) {
            logger.error('Single check failed:', { error: error.message });
            throw error;
        }
    }
    async listConfigurations() {
        try {
            await this.monitorService.initialize();
            const configs = this.monitorService.getConfigurations();
            if (configs.length === 0) {
                console.log('No flight configurations found.');
                return;
            }
            console.log('\n📋 Flight Monitor Configurations:\n');
            configs.forEach((config, index) => {
                console.log(`${index + 1}. ${config.name} (${config.active ? 'Active' : 'Inactive'})`);
                console.log(`   ID: ${config.id}`);
                console.log(`   Route: ${config.search.originCode} → ${config.search.destinationCode}`);
                console.log(`   Date: ${config.search.departureDate}`);
                console.log(`   Target Flights: ${config.targetFlights.join(', ') || 'Any'}`);
                console.log(`   Target Times: ${config.targetTimes.join(', ') || 'Any'}`);
                console.log(`   Notifications: Email=${config.notifications.email}, SMS=${config.notifications.sms}`);
                console.log(`   Created: ${new Date(config.createdAt).toLocaleString()}`);
                if (config.lastChecked) {
                    console.log(`   Last Checked: ${new Date(config.lastChecked).toLocaleString()}`);
                }
                if (config.lastAvailable) {
                    console.log(`   Last Available: ${new Date(config.lastAvailable).toLocaleString()}`);
                }
                console.log('');
            });
        }
        catch (error) {
            logger.error('Failed to list configurations:', { error: error.message });
            throw error;
        }
    }
}
// CLI handling
async function main() {
    const app = new FlightMonitorApp();
    const command = process.argv[2];
    switch (command) {
        case 'check':
            await app.runSingleCheck();
            break;
        case 'list':
            await app.listConfigurations();
            break;
        case 'start':
        default:
            await app.start();
            break;
    }
}
// Only run if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
    main().catch((error) => {
        console.error('Application failed:', error.message);
        process.exit(1);
    });
}
export { FlightMonitorApp };
