#!/usr/bin/env node

// Simple test script to verify the MCP server is working
import { spawn } from 'child_process';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

console.log('Testing MySQL MCP Server...');

// Start the server
const serverProcess = spawn('node', [join(__dirname, 'dist/index.js')], {
  stdio: ['pipe', 'pipe', 'pipe']
});

// Test the list tools functionality
const testMessage = {
  jsonrpc: '2.0',
  id: 1,
  method: 'tools/list',
  params: {}
};

serverProcess.stdin.write(JSON.stringify(testMessage) + '\n');

let output = '';
serverProcess.stdout.on('data', (data) => {
  output += data.toString();
  
  // Look for a complete JSON response
  try {
    const lines = output.split('\n').filter(line => line.trim());
    for (const line of lines) {
      if (line.startsWith('{')) {
        const response = JSON.parse(line);
        if (response.id === 1) {
          console.log('✅ Server responded successfully!');
          console.log('Available tools:');
          response.result.tools.forEach(tool => {
            console.log(`  - ${tool.name}: ${tool.description}`);
          });
          serverProcess.kill();
          process.exit(0);
        }
      }
    }
  } catch (e) {
    // Continue waiting for complete response
  }
});

serverProcess.stderr.on('data', (data) => {
  console.log('Server stderr:', data.toString());
});

serverProcess.on('close', (code) => {
  if (code !== 0) {
    console.log('❌ Server exited with code:', code);
  }
});

// Timeout after 10 seconds
setTimeout(() => {
  console.log('❌ Test timed out');
  serverProcess.kill();
  process.exit(1);
}, 10000);
