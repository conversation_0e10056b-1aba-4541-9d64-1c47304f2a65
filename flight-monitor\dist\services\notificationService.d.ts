import { NotificationData } from '../types/flight.types.js';
export declare class NotificationService {
    private emailTransporter;
    private sns;
    constructor();
    private initializeEmailService;
    private initializeSMSService;
    sendFlightAvailableNotification(data: NotificationData): Promise<void>;
    private sendEmailNotification;
    private sendSMSNotification;
    private sendSNSMessage;
    private sendTwilioMessage;
    testEmailConnection(): Promise<boolean>;
    testSMSConnection(): Promise<boolean>;
}
