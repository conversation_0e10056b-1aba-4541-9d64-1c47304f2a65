#!/usr/bin/env node

import dotenv from 'dotenv';
dotenv.config();

console.log('🛫 Testing with REAL flight data from Turkish Airlines website...\n');

try {
  const { NotificationService } = await import('./dist/services/notificationService.js');
  const notificationService = new NotificationService();
  
  // Real flight data from the Turkish Airlines website you just showed
  const realFlightData = {
    configId: 'istanbul-nevsehir-real',
    configName: 'Istanbul to Nevsehir - September 19, 2025',
    flight: {
      segments: [{
        flightNumber: 'TK2010', // Assuming this is the 08:40 flight
        departureAirport: 'IST',
        arrivalAirport: 'NAV',
        departureDateTime: '2025-09-19T08:40:00Z',
        arrivalDateTime: '2025-09-19T10:00:00Z',
        duration: '1h 20m',
        aircraft: 'Boeing B737-800',
        operatingAirline: 'TK',
        bookingClasses: [
          { code: 'Y', availableSeats: '9', status: 'A' }
        ]
      }],
      totalDuration: '1h 20m',
      stops: 0,
      available: true,
      price: {
        amount: 5352.84,
        currency: 'TRY'
      }
    },
    message: '🎉 FLIGHT AVAILABLE! Your monitored flight TK2010 (08:40 IST → 10:00 NAV) is now available for booking at TRY 5,352.84',
    timestamp: new Date().toISOString()
  };
  
  console.log('✈️ Flight Details from Turkish Airlines Website:');
  console.log(`- Flight: ${realFlightData.flight.segments[0].flightNumber}`);
  console.log(`- Route: ${realFlightData.flight.segments[0].departureAirport} → ${realFlightData.flight.segments[0].arrivalAirport}`);
  console.log(`- Departure: ${new Date(realFlightData.flight.segments[0].departureDateTime).toLocaleString()}`);
  console.log(`- Arrival: ${new Date(realFlightData.flight.segments[0].arrivalDateTime).toLocaleString()}`);
  console.log(`- Duration: ${realFlightData.flight.totalDuration}`);
  console.log(`- Aircraft: ${realFlightData.flight.segments[0].aircraft}`);
  console.log(`- Price: TRY ${realFlightData.flight.price.amount}`);
  console.log(`- Status: AVAILABLE ✅`);
  
  console.log('\n📱 Sending notification with real flight data...');
  
  await notificationService.sendFlightAvailableNotification(realFlightData);
  
  console.log('\n🎉 SUCCESS! This is exactly what you would receive when your flight monitor finds available flights!');
  console.log('\n📧 Check your email and phone for the notification');
  console.log('\n💡 Once the Turkish Airlines API is fixed, you will get these notifications automatically every 3 minutes when flights become available.');
  
} catch (error) {
  console.error('❌ Error:', error.message);
}
