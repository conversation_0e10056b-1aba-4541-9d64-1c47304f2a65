#!/usr/bin/env node
import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import { CallToolRequestSchema, ListToolsRequestSchema, } from '@modelcontextprotocol/sdk/types.js';
import { MySQLManager } from './mysql-tools.js';
import { DatabaseConfigSchema } from './types.js';
import { dirname } from 'path';
import { fileURLToPath } from 'url';
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
class MySQLMCPServer {
    server;
    mysqlManager = null;
    constructor() {
        this.server = new Server({
            name: 'mysql-mcp-server',
            version: '1.0.0',
        });
        this.setupToolHandlers();
        this.setupErrorHandling();
    }
    setupErrorHandling() {
        this.server.onerror = (error) => {
            console.error('[MCP Error]', error);
        };
        process.on('SIGINT', async () => {
            if (this.mysqlManager) {
                await this.mysqlManager.disconnect();
            }
            await this.server.close();
            process.exit(0);
        });
    }
    setupToolHandlers() {
        this.server.setRequestHandler(ListToolsRequestSchema, async () => {
            return {
                tools: [
                    {
                        name: 'mysql_connect',
                        description: 'Connect to a MySQL database',
                        inputSchema: {
                            type: 'object',
                            properties: {
                                host: { type: 'string', description: 'Database host', default: 'localhost' },
                                port: { type: 'number', description: 'Database port', default: 3306 },
                                user: { type: 'string', description: 'Database username' },
                                password: { type: 'string', description: 'Database password' },
                                database: { type: 'string', description: 'Database name (optional)' },
                                ssl: { type: 'boolean', description: 'Use SSL connection', default: false },
                            },
                            required: ['user', 'password'],
                        },
                    },
                    {
                        name: 'mysql_query',
                        description: 'Execute a SQL query',
                        inputSchema: {
                            type: 'object',
                            properties: {
                                query: { type: 'string', description: 'SQL query to execute' },
                                params: { type: 'array', description: 'Query parameters', items: { type: 'string' } },
                            },
                            required: ['query'],
                        },
                    },
                    {
                        name: 'mysql_list_databases',
                        description: 'List all databases',
                        inputSchema: {
                            type: 'object',
                            properties: {},
                        },
                    },
                    {
                        name: 'mysql_list_tables',
                        description: 'List all tables in a database',
                        inputSchema: {
                            type: 'object',
                            properties: {
                                database: { type: 'string', description: 'Database name (optional)' },
                            },
                        },
                    },
                    {
                        name: 'mysql_describe_table',
                        description: 'Get detailed information about a table',
                        inputSchema: {
                            type: 'object',
                            properties: {
                                table: { type: 'string', description: 'Table name' },
                                database: { type: 'string', description: 'Database name (optional)' },
                            },
                            required: ['table'],
                        },
                    },
                    {
                        name: 'mysql_database_stats',
                        description: 'Get database statistics',
                        inputSchema: {
                            type: 'object',
                            properties: {
                                database: { type: 'string', description: 'Database name (optional)' },
                            },
                        },
                    },
                    {
                        name: 'mysql_connection_info',
                        description: 'Get current connection information',
                        inputSchema: {
                            type: 'object',
                            properties: {},
                        },
                    },
                    {
                        name: 'mysql_test_connection',
                        description: 'Test the current database connection',
                        inputSchema: {
                            type: 'object',
                            properties: {},
                        },
                    },
                    {
                        name: 'mysql_disconnect',
                        description: 'Disconnect from the database',
                        inputSchema: {
                            type: 'object',
                            properties: {},
                        },
                    },
                ],
            };
        });
        this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
            const { name, arguments: args } = request.params;
            try {
                switch (name) {
                    case 'mysql_connect':
                        return await this.handleConnect(args);
                    case 'mysql_query':
                        return await this.handleQuery(args);
                    case 'mysql_list_databases':
                        return await this.handleListDatabases();
                    case 'mysql_list_tables':
                        return await this.handleListTables(args);
                    case 'mysql_describe_table':
                        return await this.handleDescribeTable(args);
                    case 'mysql_database_stats':
                        return await this.handleDatabaseStats(args);
                    case 'mysql_connection_info':
                        return await this.handleConnectionInfo();
                    case 'mysql_test_connection':
                        return await this.handleTestConnection();
                    case 'mysql_disconnect':
                        return await this.handleDisconnect();
                    default:
                        throw new Error(`Unknown tool: ${name}`);
                }
            }
            catch (error) {
                return {
                    content: [
                        {
                            type: 'text',
                            text: `Error: ${error instanceof Error ? error.message : String(error)}`,
                        },
                    ],
                };
            }
        });
    }
    async handleConnect(args) {
        const config = DatabaseConfigSchema.parse(args);
        this.mysqlManager = new MySQLManager(config);
        await this.mysqlManager.connect();
        return {
            content: [
                {
                    type: 'text',
                    text: `Successfully connected to MySQL database at ${config.host}:${config.port}`,
                },
            ],
        };
    }
    async handleQuery(args) {
        if (!this.mysqlManager) {
            throw new Error('Not connected to database. Use mysql_connect first.');
        }
        const { query, params = [] } = args;
        const result = await this.mysqlManager.executeQuery(query, params);
        return {
            content: [
                {
                    type: 'text',
                    text: JSON.stringify(result, null, 2),
                },
            ],
        };
    }
    async handleListDatabases() {
        if (!this.mysqlManager) {
            throw new Error('Not connected to database. Use mysql_connect first.');
        }
        const databases = await this.mysqlManager.listDatabases();
        return {
            content: [
                {
                    type: 'text',
                    text: `Databases:\n${databases.map(db => `- ${db}`).join('\n')}`,
                },
            ],
        };
    }
    async handleListTables(args) {
        if (!this.mysqlManager) {
            throw new Error('Not connected to database. Use mysql_connect first.');
        }
        const { database } = args;
        const tables = await this.mysqlManager.listTables(database);
        return {
            content: [
                {
                    type: 'text',
                    text: `Tables${database ? ` in ${database}` : ''}:\n${tables.map(table => `- ${table}`).join('\n')}`,
                },
            ],
        };
    }
    async handleDescribeTable(args) {
        if (!this.mysqlManager) {
            throw new Error('Not connected to database. Use mysql_connect first.');
        }
        const { table, database } = args;
        const tableInfo = await this.mysqlManager.getTableInfo(table, database);
        return {
            content: [
                {
                    type: 'text',
                    text: JSON.stringify(tableInfo, null, 2),
                },
            ],
        };
    }
    async handleDatabaseStats(args) {
        if (!this.mysqlManager) {
            throw new Error('Not connected to database. Use mysql_connect first.');
        }
        const { database } = args;
        const stats = await this.mysqlManager.getDatabaseStats(database);
        return {
            content: [
                {
                    type: 'text',
                    text: JSON.stringify(stats, null, 2),
                },
            ],
        };
    }
    async handleConnectionInfo() {
        if (!this.mysqlManager) {
            throw new Error('Not connected to database. Use mysql_connect first.');
        }
        const info = await this.mysqlManager.getConnectionInfo();
        return {
            content: [
                {
                    type: 'text',
                    text: JSON.stringify(info, null, 2),
                },
            ],
        };
    }
    async handleTestConnection() {
        if (!this.mysqlManager) {
            throw new Error('Not connected to database. Use mysql_connect first.');
        }
        const isConnected = await this.mysqlManager.testConnection();
        return {
            content: [
                {
                    type: 'text',
                    text: `Connection status: ${isConnected ? 'Connected' : 'Disconnected'}`,
                },
            ],
        };
    }
    async handleDisconnect() {
        if (!this.mysqlManager) {
            throw new Error('Not connected to database.');
        }
        await this.mysqlManager.disconnect();
        this.mysqlManager = null;
        return {
            content: [
                {
                    type: 'text',
                    text: 'Disconnected from MySQL database',
                },
            ],
        };
    }
    async run() {
        const transport = new StdioServerTransport();
        await this.server.connect(transport);
        console.error('MySQL MCP server running on stdio');
    }
}
const server = new MySQLMCPServer();
server.run().catch(console.error);
