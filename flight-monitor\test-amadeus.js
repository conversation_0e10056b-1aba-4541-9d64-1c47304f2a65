#!/usr/bin/env node

import dotenv from 'dotenv';
dotenv.config();

console.log('🛫 Testing Amadeus API directly...\n');

try {
  const { AmadeusService } = await import('./dist/services/amadeusService.js');
  const amadeus = new AmadeusService();
  
  console.log('✅ AmadeusService imported successfully');
  
  // Test connection
  console.log('\n🔗 Testing Amadeus connection...');
  const connectionTest = await amadeus.testConnection();
  console.log(`Connection test: ${connectionTest ? '✅ SUCCESS' : '❌ FAILED'}`);
  
  if (connectionTest) {
    console.log('\n🔍 Searching for flights IST → NAV on September 19, 2025...');
    
    const searchRequest = {
      originCode: 'IST',
      destinationCode: 'NAV',
      departureDate: '2025-09-19',
      passengers: {
        adults: 1
      },
      cabinClass: 'ECONOMY'
    };
    
    const result = await amadeus.searchFlights(searchRequest);
    
    console.log(`\n✈️ Found ${result.flights.length} flights:`);
    
    result.flights.forEach((flight, index) => {
      const segment = flight.segments[0];
      const departureTime = new Date(segment.departureDateTime);
      const arrivalTime = new Date(segment.arrivalDateTime);
      
      console.log(`\n${index + 1}. Flight ${segment.flightNumber}`);
      console.log(`   Departure: ${departureTime.toLocaleTimeString()} (${departureTime.toLocaleDateString()})`);
      console.log(`   Arrival: ${arrivalTime.toLocaleTimeString()} (${arrivalTime.toLocaleDateString()})`);
      console.log(`   Duration: ${flight.totalDuration}`);
      console.log(`   Aircraft: ${segment.aircraft}`);
      console.log(`   Available: ${flight.available ? '✅ YES' : '❌ NO'}`);
      if (flight.price) {
        console.log(`   Price: ${flight.price.amount} ${flight.price.currency}`);
      }
      
      // Check if this matches target times
      const timeString = departureTime.toTimeString().substring(0, 5);
      const targetTimes = ['08:40', '10:00'];
      if (targetTimes.includes(timeString)) {
        console.log(`   🎯 MATCHES TARGET TIME: ${timeString}`);
      }
    });
    
    console.log('\n🎯 Target Times: 08:40, 10:00');
    console.log('💡 The monitor will send notifications when flights at these times become available.');
    
  }
  
} catch (error) {
  console.error('❌ Error:', error.message);
  console.error('Stack:', error.stack);
}
