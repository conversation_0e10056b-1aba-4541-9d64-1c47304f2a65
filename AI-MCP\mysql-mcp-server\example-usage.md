# MySQL MCP Server Usage Examples

This document provides practical examples of how to use the MySQL MCP Server with AI assistants.

## Basic Connection and Queries

### 1. Connect to Database
```json
{
  "tool": "mysql_connect",
  "arguments": {
    "host": "localhost",
    "port": 3306,
    "user": "myuser",
    "password": "mypassword",
    "database": "myapp"
  }
}
```

### 2. List All Databases
```json
{
  "tool": "mysql_list_databases",
  "arguments": {}
}
```

### 3. List Tables in Current Database
```json
{
  "tool": "mysql_list_tables",
  "arguments": {}
}
```

### 4. Execute a Simple Query
```json
{
  "tool": "mysql_query",
  "arguments": {
    "query": "SELECT * FROM users LIMIT 10"
  }
}
```

### 5. Execute Query with Parameters
```json
{
  "tool": "mysql_query",
  "arguments": {
    "query": "SELECT * FROM users WHERE age > ? AND city = ?",
    "params": [25, "New York"]
  }
}
```

## Schema Exploration

### 6. Describe a Table
```json
{
  "tool": "mysql_describe_table",
  "arguments": {
    "table": "users"
  }
}
```

### 7. Get Database Statistics
```json
{
  "tool": "mysql_database_stats",
  "arguments": {}
}
```

## Data Operations

### 8. Insert Data
```json
{
  "tool": "mysql_query",
  "arguments": {
    "query": "INSERT INTO users (name, email, age) VALUES (?, ?, ?)",
    "params": ["John Doe", "<EMAIL>", 30]
  }
}
```

### 9. Update Data
```json
{
  "tool": "mysql_query",
  "arguments": {
    "query": "UPDATE users SET age = ? WHERE email = ?",
    "params": [31, "<EMAIL>"]
  }
}
```

### 10. Delete Data
```json
{
  "tool": "mysql_query",
  "arguments": {
    "query": "DELETE FROM users WHERE age < ?",
    "params": [18]
  }
}
```

## Advanced Queries

### 11. Join Tables
```json
{
  "tool": "mysql_query",
  "arguments": {
    "query": "SELECT u.name, p.title FROM users u JOIN posts p ON u.id = p.user_id WHERE u.active = 1"
  }
}
```

### 12. Aggregate Functions
```json
{
  "tool": "mysql_query",
  "arguments": {
    "query": "SELECT COUNT(*) as total_users, AVG(age) as avg_age FROM users"
  }
}
```

### 13. Create Table
```json
{
  "tool": "mysql_query",
  "arguments": {
    "query": "CREATE TABLE products (id INT AUTO_INCREMENT PRIMARY KEY, name VARCHAR(255) NOT NULL, price DECIMAL(10,2), created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP)"
  }
}
```

## Connection Management

### 14. Test Connection
```json
{
  "tool": "mysql_test_connection",
  "arguments": {}
}
```

### 15. Get Connection Info
```json
{
  "tool": "mysql_connection_info",
  "arguments": {}
}
```

### 16. Disconnect
```json
{
  "tool": "mysql_disconnect",
  "arguments": {}
}
```

## Common Use Cases

### Database Migration Check
1. Connect to database
2. List all tables
3. Describe each table to check schema
4. Get database stats to check data volume

### Data Analysis
1. Connect to database
2. Execute aggregate queries
3. Join multiple tables for comprehensive analysis
4. Export results for further processing

### Database Maintenance
1. Check connection status
2. Get database statistics
3. Identify large tables
4. Run optimization queries

## Error Handling

The server provides detailed error messages for common issues:

- **Connection errors**: Invalid credentials, network issues
- **Query errors**: Syntax errors, permission issues
- **Parameter errors**: Type mismatches, missing required fields

Always check the response for error messages and handle them appropriately in your application.
