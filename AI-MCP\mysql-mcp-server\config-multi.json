{"connections": {"dev": {"host": "dev-test.ciqdozidkrkz.us-east-1.rds.amazonaws.com", "port": 3306, "user": "admin", "password": "y$7EGSwmFws1DDB?[$tR|i.AjU[f", "database": "", "ssl": true, "connectionLimit": 10, "acquireTimeout": 60000, "timeout": 60000}, "prod": {"host": "prod-server.example.com", "port": 3306, "user": "prod_user", "password": "prod_password", "database": "production_db", "ssl": true, "connectionLimit": 5, "acquireTimeout": 30000, "timeout": 30000}, "local": {"host": "localhost", "port": 3306, "user": "root", "password": "local_password", "database": "test_db", "ssl": false, "connectionLimit": 5, "acquireTimeout": 10000, "timeout": 10000}}, "default": "dev"}