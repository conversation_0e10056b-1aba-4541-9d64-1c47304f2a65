# Turkish Airlines API Configuration
TK_API_KEY=l7xxf65655256fbc4bf3a
TK_API_SECRET=e1b95282b3201e3
TK_API_BASE_URL=https://api.turkishairlines.com

# Notification Settings
NOTIFICATION_EMAIL=<EMAIL>
NOTIFICATION_PHONE=+***********

# Email Configuration (Choose one method)
# Method 1: Gmail SMTP
GMAIL_USER=<EMAIL>
GMAIL_APP_PASSWORD=CiscoNerd1987$$

# Method 2: AWS SES
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=********************
AWS_SECRET_ACCESS_KEY=4Xn8pYmHrH7LKjcOHXWNTUObOePq5thnPzbPjoa/
SES_FROM_EMAIL=<EMAIL>

# SMS Configuration (Choose one method)
# Method 1: AWS SNS
SNS_TOPIC_ARN=arn:aws:sns:us-east-1:************:flight-alerts
# Method 2: Twilio
TWILIO_ACCOUNT_SID=your-twilio-account-sid
TWILIO_AUTH_TOKEN=your-twilio-auth-token
TWILIO_FROM_PHONE=+**********

# Monitoring Configuration
CHECK_INTERVAL_MINUTES=30
LOG_LEVEL=info
ENABLE_EMAIL_NOTIFICATIONS=true
ENABLE_SMS_NOTIFICATIONS=true

# Development Settings
NODE_ENV=development
