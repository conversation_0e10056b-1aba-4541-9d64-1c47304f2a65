{"name": "turkish-airlines-flight-monitor", "version": "1.0.0", "description": "Automated flight availability monitoring for Turkish Airlines", "main": "dist/index.js", "type": "module", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "tsx src/index.ts", "watch": "tsx watch src/index.ts", "test": "node --test", "setup": "node setup.js"}, "keywords": ["turkish-airlines", "flight-monitor", "automation", "notifications", "travel"], "author": "Flight Monitor System", "license": "MIT", "dependencies": {"aws-sdk": "^2.1490.0", "axios": "^1.9.0", "dotenv": "^16.3.1", "node-cron": "^3.0.3", "nodemailer": "^6.9.7", "winston": "^3.11.0", "zod": "^3.22.4"}, "devDependencies": {"@types/node": "^20.10.0", "@types/node-cron": "^3.0.11", "@types/nodemailer": "^6.4.14", "tsx": "^4.6.0", "typescript": "^5.3.0"}, "engines": {"node": ">=18.0.0"}}