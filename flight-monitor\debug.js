#!/usr/bin/env node

import dotenv from 'dotenv';
dotenv.config();

console.log('🐛 Debug: Starting flight monitor...');

try {
  console.log('🐛 Debug: Loading modules...');
  
  // Test basic imports
  const { FlightMonitorApp } = await import('./dist/index.js');
  console.log('🐛 Debug: FlightMonitorApp imported successfully');
  
  // Test configuration validation
  const { validateConfig } = await import('./dist/config/apiConfig.js');
  console.log('🐛 Debug: validateConfig imported successfully');
  
  const configValidation = validateConfig();
  console.log('🐛 Debug: Configuration validation:', configValidation);
  
  if (!configValidation.valid) {
    console.log('❌ Configuration errors:', configValidation.errors);
    process.exit(1);
  }
  
  console.log('🐛 Debug: Creating FlightMonitorApp instance...');
  const app = new FlightMonitorApp();
  
  console.log('🐛 Debug: Running single check...');
  await app.runSingleCheck();
  
  console.log('✅ Debug: Single check completed successfully!');
  
} catch (error) {
  console.error('❌ Debug: Error occurred:', error);
  console.error('Stack trace:', error.stack);
}
