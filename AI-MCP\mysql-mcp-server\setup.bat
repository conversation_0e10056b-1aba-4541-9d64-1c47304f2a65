@echo off
echo 🚀 Setting up MySQL MCP Server...

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js is not installed. Please install Node.js 18+ first.
    pause
    exit /b 1
)

echo ✅ Node.js detected

REM Install dependencies
echo 📦 Installing dependencies...
call npm install
if %errorlevel% neq 0 (
    echo ❌ Failed to install dependencies
    pause
    exit /b 1
)

echo ✅ Dependencies installed

REM Build the project
echo 🔨 Building TypeScript...
call npm run build
if %errorlevel% neq 0 (
    echo ❌ Build failed
    pause
    exit /b 1
)

echo ✅ Build completed

REM Test the server
echo 🧪 Testing server...
node test-server.js
if %errorlevel% neq 0 (
    echo ❌ Server test failed
    pause
    exit /b 1
)

echo ✅ Server test passed

REM Create config file if it doesn't exist
if not exist "config.json" (
    echo 📝 Creating config.json from example...
    copy config.example.json config.json
    echo ⚠️  Please edit config.json with your MySQL credentials
)

echo.
echo 🎉 MySQL MCP Server setup complete!
echo.
echo Next steps:
echo 1. Edit config.json with your MySQL database credentials
echo 2. Run the server with: npm start
echo 3. Or run in development mode with: npm run dev
echo.
echo For usage examples, see example-usage.md
pause
