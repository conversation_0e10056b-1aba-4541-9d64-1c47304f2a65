#!/usr/bin/env node

// Load environment variables
import dotenv from 'dotenv';
import fs from 'fs';

dotenv.config();

console.log('🧪 Testing Flight Monitor Configuration...\n');

// Test environment variables
const requiredVars = [
  'TK_API_KEY',
  'TK_API_SECRET', 
  'NOTIFICATION_EMAIL'
];

console.log('📋 Environment Variables:');
requiredVars.forEach(varName => {
  const value = process.env[varName];
  if (value) {
    console.log(`✅ ${varName}: ${value.substring(0, 10)}...`);
  } else {
    console.log(`❌ ${varName}: Not set`);
  }
});

console.log('\n📧 Email Configuration:');
console.log(`Gmail User: ${process.env.GMAIL_USER || 'Not set'}`);
console.log(`Gmail Password: ${process.env.GMAIL_APP_PASSWORD ? 'Set' : 'Not set'}`);

console.log('\n📱 SMS Configuration:');
console.log(`Phone: ${process.env.NOTIFICATION_PHONE || 'Not set'}`);
console.log(`SNS Topic: ${process.env.SNS_TOPIC_ARN || 'Not set'}`);

console.log('\n⚙️ Monitoring Settings:');
console.log(`Check Interval: ${process.env.CHECK_INTERVAL_MINUTES || 30} minutes`);
console.log(`Email Notifications: ${process.env.ENABLE_EMAIL_NOTIFICATIONS || 'true'}`);
console.log(`SMS Notifications: ${process.env.ENABLE_SMS_NOTIFICATIONS || 'true'}`);

// Test configuration file
console.log('\n📄 Configuration File:');
try {
  const configPath = './src/config/flight-configs.json';
  const config = JSON.parse(fs.readFileSync(configPath, 'utf8'));
  
  console.log(`✅ Found ${config.length} flight configuration(s)`);
  
  config.forEach((flight, index) => {
    console.log(`\n${index + 1}. ${flight.name}`);
    console.log(`   Route: ${flight.search.originCode} → ${flight.search.destinationCode}`);
    console.log(`   Date: ${flight.search.departureDate}`);
    console.log(`   Target Flights: ${flight.targetFlights.join(', ') || 'Any'}`);
    console.log(`   Target Times: ${flight.targetTimes?.join(', ') || 'Any'}`);
    console.log(`   Active: ${flight.active ? 'Yes' : 'No'}`);
  });
  
} catch (error) {
  console.log(`❌ Error reading configuration: ${error.message}`);
}

console.log('\n🚀 Ready to start monitoring!');
console.log('\nNext steps:');
console.log('1. Run: npm start (for continuous monitoring)');
console.log('2. Or run: node dist/index.js check (for single check)');
