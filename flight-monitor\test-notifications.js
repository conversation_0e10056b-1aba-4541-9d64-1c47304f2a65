#!/usr/bin/env node

import dotenv from 'dotenv';
dotenv.config();

console.log('📧 Testing Notification System...\n');

try {
  // Import notification service
  const { NotificationService } = await import('./dist/services/notificationService.js');
  
  console.log('✅ NotificationService imported successfully');
  
  // Create notification service instance
  const notificationService = new NotificationService();
  
  console.log('✅ NotificationService initialized');
  
  // Test email connection
  console.log('\n📧 Testing email connection...');
  const emailTest = await notificationService.testEmailConnection();
  console.log(`Email test result: ${emailTest ? '✅ SUCCESS' : '❌ FAILED'}`);
  
  // Test SMS connection
  console.log('\n📱 Testing SMS connection...');
  const smsTest = await notificationService.testSMSConnection();
  console.log(`SMS test result: ${smsTest ? '✅ SUCCESS' : '❌ FAILED'}`);
  
  // Create a mock flight notification
  const mockFlightData = {
    configId: 'test-notification',
    configName: 'TEST: Notification System',
    flight: {
      segments: [{
        flightNumber: 'TK2010',
        departureAirport: 'IST',
        arrivalAirport: 'NAV',
        departureDateTime: '2025-09-19T08:40:00Z',
        arrivalDateTime: '2025-09-19T10:00:00Z',
        duration: '1h 20m',
        aircraft: 'A320',
        operatingAirline: 'TK',
        bookingClasses: [
          { code: 'Y', availableSeats: '9', status: 'A' }
        ]
      }],
      totalDuration: '1h 20m',
      stops: 0,
      available: true
    },
    message: 'Test flight notification - your monitoring system is working!',
    timestamp: new Date().toISOString()
  };
  
  console.log('\n🧪 Sending test notification...');
  console.log('Flight Details:');
  console.log(`- Flight: ${mockFlightData.flight.segments[0].flightNumber}`);
  console.log(`- Route: ${mockFlightData.flight.segments[0].departureAirport} → ${mockFlightData.flight.segments[0].arrivalAirport}`);
  console.log(`- Departure: ${new Date(mockFlightData.flight.segments[0].departureDateTime).toLocaleString()}`);
  
  // Send test notification
  await notificationService.sendFlightAvailableNotification(mockFlightData);
  
  console.log('\n🎉 Test notification sent successfully!');
  console.log('\nCheck your:');
  console.log('📧 Email inbox for the flight notification');
  console.log('📱 Phone for SMS notification');
  
} catch (error) {
  console.error('❌ Error testing notifications:', error.message);
  console.error('Stack trace:', error.stack);
}
