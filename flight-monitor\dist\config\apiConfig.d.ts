import { ApiConfig, NotificationConfig } from '../types/flight.types.js';
export declare const apiConfig: ApiConfig;
export declare const notificationConfig: NotificationConfig;
export declare const monitoringConfig: {
    checkIntervalMinutes: number;
    maxRetries: number;
    retryDelayMs: number;
    requestTimeoutMs: number;
};
export declare function validateConfig(): {
    valid: boolean;
    errors: string[];
};
