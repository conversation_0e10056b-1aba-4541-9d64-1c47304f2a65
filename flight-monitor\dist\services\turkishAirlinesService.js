import axios from 'axios';
import { apiConfig } from '../config/apiConfig.js';
import logger from '../utils/logger.js';
export class TurkishAirlinesService {
    client;
    constructor() {
        this.client = axios.create({
            baseURL: apiConfig.baseUrl,
            timeout: apiConfig.timeout,
            headers: {
                'Content-Type': 'application/json',
                'apikey': apiConfig.apiKey,
                'apisecret': apiConfig.apiSecret
            }
        });
        // Add request interceptor for logging
        this.client.interceptors.request.use((config) => {
            logger.debug('Turkish Airlines API Request', {
                url: config.url,
                method: config.method,
                headers: { ...config.headers, apikey: '[REDACTED]', apisecret: '[REDACTED]' }
            });
            return config;
        }, (error) => {
            logger.error('Turkish Airlines API Request Error', error);
            return Promise.reject(error);
        });
        // Add response interceptor for logging
        this.client.interceptors.response.use((response) => {
            logger.debug('Turkish Airlines API Response', {
                status: response.status,
                statusText: response.statusText,
                data: response.data
            });
            return response;
        }, (error) => {
            logger.error('Turkish Airlines API Response Error', {
                status: error.response?.status,
                statusText: error.response?.statusText,
                data: error.response?.data,
                message: error.message
            });
            return Promise.reject(error);
        });
    }
    async searchFlights(searchRequest) {
        try {
            logger.info('Searching flights', { searchRequest });
            const requestPayload = this.buildApiRequest(searchRequest);
            const response = await this.client.post('/test/getAvailability', requestPayload);
            if (response.data.status !== 'SUCCESS') {
                throw new Error(`API returned error: ${response.data.message.Description}`);
            }
            const flights = this.parseFlightResponse(response.data);
            logger.info('Flight search completed', {
                flightCount: flights.length,
                searchId: response.data.requestId
            });
            return {
                flights,
                searchId: response.data.requestId,
                timestamp: new Date().toISOString()
            };
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : String(error);
            logger.error('Flight search failed', { error: errorMessage, searchRequest });
            throw new Error(`Flight search failed: ${errorMessage}`);
        }
    }
    buildApiRequest(searchRequest) {
        // Convert date from YYYY-MM-DD to DDMMM format (e.g., "2025-09-19" to "19SEP")
        const date = new Date(searchRequest.departureDate);
        const day = date.getDate().toString();
        const monthNames = ["JAN", "FEB", "MAR", "APR", "MAY", "JUN",
            "JUL", "AUG", "SEP", "OCT", "NOV", "DEC"];
        const month = monthNames[date.getMonth()];
        const formattedDate = `${day}${month}`;
        return {
            requestHeader: {
                clientUsername: "WEBSDOM",
                clientTransactionId: `flight-monitor-${Date.now()}`,
                channel: "WEB",
                languageCode: "EN",
                airlineCode: "TK"
            },
            OriginDestinationInformation: [
                {
                    OriginLocation: {
                        LocationCode: searchRequest.originCode,
                        MultiAirportCityInd: true
                    },
                    DestinationLocation: {
                        LocationCode: searchRequest.destinationCode,
                        MultiAirportCityInd: true
                    },
                    DepartureDateTime: {
                        Date: formattedDate,
                        WindowBefore: "P3D",
                        WindowAfter: "P3D"
                    },
                    CabinPreferences: [
                        {
                            Cabin: searchRequest.cabinClass || "ECONOMY"
                        }
                    ]
                }
            ],
            PassengerTypeQuantity: [
                {
                    Code: "ADULT",
                    Quantity: searchRequest.passengers.adults
                }
            ],
            ReducedDataIndicator: false,
            RoutingType: "O"
        };
    }
    parseFlightResponse(apiResponse) {
        const flights = [];
        try {
            const routes = apiResponse.data.availabilityOTAResponse?.createOTAAirRoute || [];
            for (const route of routes) {
                const originDestInfo = route.OTA_AirAvailRS.OriginDestinationInformation;
                const options = originDestInfo.OriginDestinationOptions.OriginDestinationOption;
                for (const option of options) {
                    const segments = option.FlightSegment.map((segment) => ({
                        flightNumber: segment.FlightNumber,
                        departureAirport: segment.DepartureAirport.LocationCode,
                        arrivalAirport: segment.ArrivalAirport.LocationCode,
                        departureDateTime: segment.DepartureDateTime,
                        arrivalDateTime: segment.ArrivalDateTime,
                        duration: segment.JourneyDuration,
                        aircraft: segment.Equipment?.AirEquipType || 'Unknown',
                        operatingAirline: segment.OperatingAirline?.CompanyShortName || 'TK',
                        bookingClasses: segment.BookingClassAvail?.map(bc => ({
                            code: bc.ResBookDesigCode,
                            availableSeats: bc.ResBookDesigQuantity,
                            status: bc.ResBookDesigStatusCode
                        })) || []
                    }));
                    // Check if flight has available seats
                    const hasAvailableSeats = segments.some(segment => segment.bookingClasses.some(bc => bc.status === 'A' && parseInt(bc.availableSeats) > 0));
                    flights.push({
                        segments,
                        totalDuration: this.calculateTotalDuration(segments),
                        stops: segments.length - 1,
                        available: hasAvailableSeats
                    });
                }
            }
            return flights;
        }
        catch (error) {
            logger.error('Error parsing flight response', { error: error instanceof Error ? error.message : String(error) });
            return [];
        }
    }
    calculateTotalDuration(segments) {
        if (segments.length === 0)
            return '0h 0m';
        const firstDeparture = new Date(segments[0].departureDateTime);
        const lastArrival = new Date(segments[segments.length - 1].arrivalDateTime);
        const totalMinutes = Math.floor((lastArrival.getTime() - firstDeparture.getTime()) / (1000 * 60));
        const hours = Math.floor(totalMinutes / 60);
        const minutes = totalMinutes % 60;
        return `${hours}h ${minutes}m`;
    }
    async testConnection() {
        try {
            // Test with a simple search
            const testSearch = {
                originCode: 'IST',
                destinationCode: 'AYT',
                departureDate: '2025-01-01',
                passengers: { adults: 1 }
            };
            await this.searchFlights(testSearch);
            logger.info('Turkish Airlines API connection test successful');
            return true;
        }
        catch (error) {
            logger.error('Turkish Airlines API connection test failed', { error: error instanceof Error ? error.message : String(error) });
            return false;
        }
    }
}
