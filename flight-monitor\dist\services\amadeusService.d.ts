import { FlightSearchRequest, FlightSearchResponse } from '../types/flight.types.js';
export declare class AmadeusService {
    private client;
    private config;
    private accessToken;
    private tokenExpiry;
    constructor();
    private getAccessToken;
    searchFlights(searchRequest: FlightSearchRequest): Promise<FlightSearchResponse>;
    private buildSearchParams;
    private parseFlightResponse;
    testConnection(): Promise<boolean>;
}
