#!/usr/bin/env node

import dotenv from 'dotenv';
dotenv.config();

console.log('🔍 Testing flight availability details...\n');

try {
  const { AmadeusService } = await import('./dist/services/amadeusService.js');
  const amadeus = new AmadeusService();
  
  console.log('✅ AmadeusService imported successfully');
  
  console.log('\n🔍 Searching for flights IST → NAV on September 19, 2025...');
  
  const searchRequest = {
    originCode: 'IST',
    destinationCode: 'NAV',
    departureDate: '2025-09-19',
    passengers: {
      adults: 1
    },
    cabinClass: 'ECONOMY'
  };
  
  const result = await amadeus.searchFlights(searchRequest);
  
  console.log(`\n✈️ Found ${result.flights.length} flights:\n`);
  
  result.flights.forEach((flight, index) => {
    const segment = flight.segments[0];
    const departureTime = new Date(segment.departureDateTime);
    const timeString = departureTime.toTimeString().substring(0, 5);
    
    console.log(`${index + 1}. Flight ${segment.flightNumber}`);
    console.log(`   Departure: ${timeString} (${departureTime.toLocaleDateString()})`);
    console.log(`   Available: ${flight.available ? '✅ YES' : '❌ NO'}`);
    
    // Show detailed availability info
    if (segment.bookingClasses && segment.bookingClasses.length > 0) {
      console.log(`   Available Seats: ${segment.bookingClasses[0].availableSeats}`);
      console.log(`   Booking Status: ${segment.bookingClasses[0].status}`);
    }
    
    if (flight.price) {
      console.log(`   Price: ${flight.price.amount} ${flight.price.currency}`);
    }
    
    // Check if this matches target times
    const targetTimes = ['11:15', '13:30'];
    if (targetTimes.includes(timeString)) {
      console.log(`   🎯 MATCHES TARGET TIME: ${timeString}`);
      
      if (flight.available) {
        console.log(`   📢 WOULD SEND NOTIFICATION: Flight available with ${segment.bookingClasses[0]?.availableSeats || 'unknown'} seats`);
      } else {
        console.log(`   🚫 NO NOTIFICATION: Flight not available`);
      }
    }
    
    console.log('');
  });
  
  console.log('🎯 Target Times: 11:15, 13:30');
  console.log('\n💡 Analysis:');
  console.log('- Flights marked as "available: true" will trigger notifications');
  console.log('- Flights marked as "available: false" will be ignored');
  console.log('- Available seats count shows how many seats are bookable');
  
} catch (error) {
  console.error('❌ Error:', error.message);
  console.error('Stack:', error.stack);
}
