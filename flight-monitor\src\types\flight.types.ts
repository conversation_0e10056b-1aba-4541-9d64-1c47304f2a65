export interface FlightSearchRequest {
  originCode: string;
  destinationCode: string;
  departureDate: string;
  returnDate?: string;
  passengers: {
    adults: number;
    children?: number;
    infants?: number;
  };
  cabinClass?: 'ECONOMY' | 'BUSINESS' | 'FIRST';
}

export interface FlightSegment {
  flightNumber: string;
  departureAirport: string;
  arrivalAirport: string;
  departureDateTime: string;
  arrivalDateTime: string;
  duration: string;
  aircraft: string;
  operatingAirline: string;
  availableSeats?: number;
  bookingClasses: BookingClass[];
}

export interface BookingClass {
  code: string;
  availableSeats: string;
  status: string;
}

export interface FlightOption {
  segments: FlightSegment[];
  totalDuration: string;
  stops: number;
  price?: {
    amount: number;
    currency: string;
  };
  available: boolean;
}

export interface FlightSearchResponse {
  flights: FlightOption[];
  searchId: string;
  timestamp: string;
}

export interface FlightMonitorConfig {
  id: string;
  name: string;
  search: FlightSearchRequest;
  targetFlights: string[]; // Flight numbers to monitor
  targetTimes?: string[]; // Specific departure times to monitor
  notifications: {
    email: boolean;
    sms: boolean;
    webhook?: string;
  };
  active: boolean;
  createdAt: string;
  lastChecked?: string;
  lastAvailable?: string;
}

export interface NotificationData {
  configId: string;
  configName: string;
  flight: FlightOption;
  message: string;
  timestamp: string;
}

export interface TurkishAirlinesApiResponse {
  status: string;
  message: {
    Code: string;
    Description: string;
  };
  data: {
    availabilityOTAResponse?: {
      createOTAAirRoute: Array<{
        OTA_AirAvailRS: {
          OriginDestinationInformation: {
            OriginDestinationOptions: {
              OriginDestinationOption: Array<{
                FlightSegment: ApiFlightSegment[];
              }>;
            };
          };
        };
      }>;
    };
  };
  requestId: string;
}

export interface ApiFlightSegment {
  FlightNumber: string;
  DepartureAirport: {
    LocationCode: string;
  };
  ArrivalAirport: {
    LocationCode: string;
  };
  DepartureDateTime: string;
  ArrivalDateTime: string;
  JourneyDuration: string;
  Equipment?: {
    AirEquipType: string;
  };
  OperatingAirline?: {
    CompanyShortName: string;
  };
  BookingClassAvail?: Array<{
    ResBookDesigCode: string;
    ResBookDesigQuantity: string;
    ResBookDesigStatusCode: string;
  }>;
}

export interface ApiConfig {
  apiKey: string;
  apiSecret: string;
  baseUrl: string;
  timeout: number;
  retryAttempts: number;
}

export interface NotificationConfig {
  email: {
    enabled: boolean;
    service: 'gmail' | 'aws-ses' | 'smtp';
    from: string;
    to: string;
    smtp?: {
      host: string;
      port: number;
      secure: boolean;
      auth: {
        user: string;
        pass: string;
      };
    };
  };
  sms: {
    enabled: boolean;
    service: 'aws-sns' | 'twilio';
    to: string;
    aws?: {
      region: string;
      topicArn: string;
    };
    twilio?: {
      accountSid: string;
      authToken: string;
      from: string;
    };
  };
}

export interface MonitoringStats {
  totalChecks: number;
  successfulChecks: number;
  failedChecks: number;
  flightsFound: number;
  notificationsSent: number;
  lastCheckTime: string;
  uptime: number;
}
