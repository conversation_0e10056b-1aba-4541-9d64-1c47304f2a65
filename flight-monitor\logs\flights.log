2025-06-07 23:36:53 [INFO]: Email service initialized {
  "service": "gmail"
}
2025-06-07 23:36:53 [INFO]: AWS SNS service initialized {
  "service": "flight-monitor"
}
2025-06-07 23:36:53 [INFO]: 🔍 Running single flight check... {
  "service": "flight-monitor"
}
2025-06-07 23:36:53 [WARN]: Flight configurations file not found, creating empty configuration {
  "service": "flight-monitor"
}
2025-06-07 23:36:53 [INFO]: Flight Monitor Service initialized {
  "service": "flight-monitor",
  "configCount": 0,
  "activeConfigs": 0
}
2025-06-07 23:36:53 [INFO]: No active flight configurations to check {
  "service": "flight-monitor"
}
2025-06-07 23:36:53 [INFO]: ✅ Single check completed: {
  "service": "flight-monitor",
  "totalChecks": 0,
  "successfulChecks": 0,
  "failedChecks": 0,
  "flightsFound": 0,
  "notificationsSent": 0,
  "lastCheckTime": "",
  "uptime": 0
}
2025-06-07 23:37:21 [INFO]: Email service initialized {
  "service": "gmail"
}
2025-06-07 23:37:21 [INFO]: AWS SNS service initialized {
  "service": "flight-monitor"
}
2025-06-07 23:37:21 [INFO]: 🔍 Running single flight check... {
  "service": "flight-monitor"
}
2025-06-07 23:37:21 [INFO]: Flight configurations loaded {
  "service": "flight-monitor",
  "count": 2
}
2025-06-07 23:37:21 [INFO]: Flight Monitor Service initialized {
  "service": "flight-monitor",
  "configCount": 2,
  "activeConfigs": 1
}
2025-06-07 23:37:21 [INFO]: Starting flight availability check {
  "service": "flight-monitor",
  "configCount": 1
}
2025-06-07 23:37:21 [INFO]: Searching flights {
  "service": "flight-monitor",
  "searchRequest": {
    "originCode": "IST",
    "destinationCode": "NAV",
    "departureDate": "2025-09-19",
    "passengers": {
      "adults": 1
    },
    "cabinClass": "ECONOMY"
  }
}
2025-06-07 23:37:22 [ERROR]: Turkish Airlines API Response Error Request failed with status code 400 {
  "service": "flight-monitor",
  "status": 400,
  "statusText": "Bad Request",
  "data": "Invalid API Key"
}
2025-06-07 23:37:22 [ERROR]: Flight search failed {
  "service": "flight-monitor",
  "error": "Request failed with status code 400",
  "searchRequest": {
    "originCode": "IST",
    "destinationCode": "NAV",
    "departureDate": "2025-09-19",
    "passengers": {
      "adults": 1
    },
    "cabinClass": "ECONOMY"
  }
}
2025-06-07 23:37:22 [ERROR]: Failed to check flight configuration {
  "service": "flight-monitor",
  "configId": "istanbul-nevsehir-test",
  "error": "Flight search failed: Request failed with status code 400"
}
2025-06-07 23:37:22 [INFO]: Flight availability check completed {
  "service": "flight-monitor"
}
2025-06-07 23:37:22 [INFO]: ✅ Single check completed: {
  "service": "flight-monitor",
  "totalChecks": 1,
  "successfulChecks": 1,
  "failedChecks": 0,
  "flightsFound": 0,
  "notificationsSent": 0,
  "lastCheckTime": "2025-06-08T03:37:21.935Z",
  "uptime": 0
}
2025-06-07 23:38:24 [INFO]: Email service initialized {
  "service": "gmail"
}
2025-06-07 23:38:24 [INFO]: AWS SNS service initialized {
  "service": "flight-monitor"
}
2025-06-07 23:38:24 [INFO]: 🔍 Running single flight check... {
  "service": "flight-monitor"
}
2025-06-07 23:38:24 [INFO]: Flight configurations loaded {
  "service": "flight-monitor",
  "count": 2
}
2025-06-07 23:38:24 [INFO]: Flight Monitor Service initialized {
  "service": "flight-monitor",
  "configCount": 2,
  "activeConfigs": 1
}
2025-06-07 23:38:24 [INFO]: Starting flight availability check {
  "service": "flight-monitor",
  "configCount": 1
}
2025-06-07 23:38:24 [INFO]: Searching flights {
  "service": "flight-monitor",
  "searchRequest": {
    "originCode": "IST",
    "destinationCode": "NAV",
    "departureDate": "2025-09-19",
    "passengers": {
      "adults": 1
    },
    "cabinClass": "ECONOMY"
  }
}
2025-06-07 23:38:25 [ERROR]: Turkish Airlines API Response Error Request failed with status code 400 {
  "service": "flight-monitor",
  "status": 400,
  "statusText": "Bad Request",
  "data": "Invalid API Secret"
}
2025-06-07 23:38:25 [ERROR]: Flight search failed {
  "service": "flight-monitor",
  "error": "Request failed with status code 400",
  "searchRequest": {
    "originCode": "IST",
    "destinationCode": "NAV",
    "departureDate": "2025-09-19",
    "passengers": {
      "adults": 1
    },
    "cabinClass": "ECONOMY"
  }
}
2025-06-07 23:38:25 [ERROR]: Failed to check flight configuration {
  "service": "flight-monitor",
  "configId": "istanbul-nevsehir-test",
  "error": "Flight search failed: Request failed with status code 400"
}
2025-06-07 23:38:25 [INFO]: Flight availability check completed {
  "service": "flight-monitor"
}
2025-06-07 23:38:25 [INFO]: ✅ Single check completed: {
  "service": "flight-monitor",
  "totalChecks": 1,
  "successfulChecks": 1,
  "failedChecks": 0,
  "flightsFound": 0,
  "notificationsSent": 0,
  "lastCheckTime": "2025-06-08T03:38:24.431Z",
  "uptime": 0
}
2025-06-07 23:41:59 [INFO]: Email service initialized {
  "service": "gmail"
}
2025-06-07 23:41:59 [INFO]: AWS SNS service initialized {
  "service": "flight-monitor"
}
2025-06-07 23:41:59 [INFO]: 🔍 Running single flight check... {
  "service": "flight-monitor"
}
2025-06-07 23:41:59 [INFO]: Flight configurations loaded {
  "service": "flight-monitor",
  "count": 2
}
2025-06-07 23:41:59 [INFO]: Flight Monitor Service initialized {
  "service": "flight-monitor",
  "configCount": 2,
  "activeConfigs": 1
}
2025-06-07 23:41:59 [INFO]: Starting flight availability check {
  "service": "flight-monitor",
  "configCount": 1
}
2025-06-07 23:41:59 [INFO]: Searching flights {
  "service": "flight-monitor",
  "searchRequest": {
    "originCode": "IST",
    "destinationCode": "NAV",
    "departureDate": "2025-09-19",
    "passengers": {
      "adults": 1
    },
    "cabinClass": "ECONOMY"
  }
}
2025-06-07 23:41:59 [ERROR]: Turkish Airlines API Response Error Request failed with status code 400 {
  "service": "flight-monitor",
  "status": 400,
  "statusText": "Bad Request",
  "data": "Invalid API Secret"
}
2025-06-07 23:41:59 [ERROR]: Flight search failed {
  "service": "flight-monitor",
  "error": "Request failed with status code 400",
  "searchRequest": {
    "originCode": "IST",
    "destinationCode": "NAV",
    "departureDate": "2025-09-19",
    "passengers": {
      "adults": 1
    },
    "cabinClass": "ECONOMY"
  }
}
2025-06-07 23:41:59 [ERROR]: Failed to check flight configuration {
  "service": "flight-monitor",
  "configId": "istanbul-nevsehir-test",
  "error": "Flight search failed: Request failed with status code 400"
}
2025-06-07 23:41:59 [INFO]: Flight availability check completed {
  "service": "flight-monitor"
}
2025-06-07 23:41:59 [INFO]: ✅ Single check completed: {
  "service": "flight-monitor",
  "totalChecks": 1,
  "successfulChecks": 1,
  "failedChecks": 0,
  "flightsFound": 0,
  "notificationsSent": 0,
  "lastCheckTime": "2025-06-08T03:41:59.088Z",
  "uptime": 0
}
2025-06-07 23:45:12 [INFO]: Email service initialized {
  "service": "gmail"
}
2025-06-07 23:45:12 [INFO]: AWS SNS service initialized {
  "service": "flight-monitor"
}
2025-06-07 23:45:12 [INFO]: 🔍 Running single flight check... {
  "service": "flight-monitor"
}
2025-06-07 23:45:12 [INFO]: Flight configurations loaded {
  "service": "flight-monitor",
  "count": 2
}
2025-06-07 23:45:12 [INFO]: Flight Monitor Service initialized {
  "service": "flight-monitor",
  "configCount": 2,
  "activeConfigs": 1
}
2025-06-07 23:45:12 [INFO]: Starting flight availability check {
  "service": "flight-monitor",
  "configCount": 1
}
2025-06-07 23:45:12 [INFO]: Searching flights {
  "service": "flight-monitor",
  "searchRequest": {
    "originCode": "IST",
    "destinationCode": "NAV",
    "departureDate": "2025-09-19",
    "passengers": {
      "adults": 1
    },
    "cabinClass": "ECONOMY"
  }
}
2025-06-07 23:45:13 [ERROR]: Turkish Airlines API Response Error Request failed with status code 400 {
  "service": "flight-monitor",
  "status": 400,
  "statusText": "Bad Request",
  "data": {
    "status": "FAILURE",
    "requestId": "000001944287d863-3c2980de",
    "message": {
      "code": "TK-BWS-10000",
      "description": "$.OriginDestinationInformation[0].DepartureDateTime.WindowAfter: is missing and it is not optional, $.OriginDestinationInformation[0].DepartureDateTime.WindowBefore: is missing and it is not optional, $.OriginDestinationInformation[0].DepartureDateTime.Date: does not match the regex pattern ^[0-9]{1,2}?[A-Z]{3}$, $.PassengerTypeQuantity[0].Code: does not have a value in the enumeration [ADULT, CHILD, INFANT, SENIOR, STUDENT, DISABLED, ATTENDANT, TEACHER, adult, child, infant, senior, student, disabled, attendant, teacher, IT_ADULT, IT_CHILD, IT_INFANT, ETHNIC, ETHNIC_CHILD, ETHNIC_INFANT, SEAMAN, LABOR, LABOR_CHILD, LABOR_INFANT, labor, labor_child, labor_infant, SOLDIER, YOUTH, YOUNGADULT, soldier, youth, youngadult]"
    },
    "data": {}
  }
}
2025-06-07 23:45:13 [ERROR]: Flight search failed {
  "service": "flight-monitor",
  "error": "Request failed with status code 400",
  "searchRequest": {
    "originCode": "IST",
    "destinationCode": "NAV",
    "departureDate": "2025-09-19",
    "passengers": {
      "adults": 1
    },
    "cabinClass": "ECONOMY"
  }
}
2025-06-07 23:45:13 [ERROR]: Failed to check flight configuration {
  "service": "flight-monitor",
  "configId": "istanbul-nevsehir-test",
  "error": "Flight search failed: Request failed with status code 400"
}
2025-06-07 23:45:13 [INFO]: Flight availability check completed {
  "service": "flight-monitor"
}
2025-06-07 23:45:13 [INFO]: ✅ Single check completed: {
  "service": "flight-monitor",
  "totalChecks": 1,
  "successfulChecks": 1,
  "failedChecks": 0,
  "flightsFound": 0,
  "notificationsSent": 0,
  "lastCheckTime": "2025-06-08T03:45:12.909Z",
  "uptime": 0
}
2025-06-07 23:46:01 [INFO]: Email service initialized {
  "service": "gmail"
}
2025-06-07 23:46:01 [INFO]: AWS SNS service initialized {
  "service": "flight-monitor"
}
2025-06-07 23:46:01 [INFO]: 🔍 Running single flight check... {
  "service": "flight-monitor"
}
2025-06-07 23:46:01 [INFO]: Flight configurations loaded {
  "service": "flight-monitor",
  "count": 2
}
2025-06-07 23:46:01 [INFO]: Flight Monitor Service initialized {
  "service": "flight-monitor",
  "configCount": 2,
  "activeConfigs": 1
}
2025-06-07 23:46:01 [INFO]: Starting flight availability check {
  "service": "flight-monitor",
  "configCount": 1
}
2025-06-07 23:46:01 [INFO]: Searching flights {
  "service": "flight-monitor",
  "searchRequest": {
    "originCode": "IST",
    "destinationCode": "NAV",
    "departureDate": "2025-09-19",
    "passengers": {
      "adults": 1
    },
    "cabinClass": "ECONOMY"
  }
}
2025-06-07 23:46:02 [ERROR]: Turkish Airlines API Response Error Request failed with status code 500 {
  "service": "flight-monitor",
  "status": 500,
  "statusText": "Internal Server Error",
  "data": {
    "status": "FAILURE",
    "requestId": "000001944287d863-3c29812c",
    "message": {
      "code": "TK-TK-BWS-99999",
      "description": "Unknown Error"
    },
    "data": {}
  }
}
2025-06-07 23:46:02 [ERROR]: Flight search failed {
  "service": "flight-monitor",
  "error": "Request failed with status code 500",
  "searchRequest": {
    "originCode": "IST",
    "destinationCode": "NAV",
    "departureDate": "2025-09-19",
    "passengers": {
      "adults": 1
    },
    "cabinClass": "ECONOMY"
  }
}
2025-06-07 23:46:02 [ERROR]: Failed to check flight configuration {
  "service": "flight-monitor",
  "configId": "istanbul-nevsehir-test",
  "error": "Flight search failed: Request failed with status code 500"
}
2025-06-07 23:46:02 [INFO]: Flight availability check completed {
  "service": "flight-monitor"
}
2025-06-07 23:46:02 [INFO]: ✅ Single check completed: {
  "service": "flight-monitor",
  "totalChecks": 1,
  "successfulChecks": 1,
  "failedChecks": 0,
  "flightsFound": 0,
  "notificationsSent": 0,
  "lastCheckTime": "2025-06-08T03:46:01.136Z",
  "uptime": 1
}
2025-06-07 23:46:36 [INFO]: Email service initialized {
  "service": "gmail"
}
2025-06-07 23:46:36 [INFO]: AWS SNS service initialized {
  "service": "flight-monitor"
}
2025-06-07 23:46:36 [INFO]: 🔍 Running single flight check... {
  "service": "flight-monitor"
}
2025-06-07 23:46:36 [INFO]: Flight configurations loaded {
  "service": "flight-monitor",
  "count": 2
}
2025-06-07 23:46:36 [INFO]: Flight Monitor Service initialized {
  "service": "flight-monitor",
  "configCount": 2,
  "activeConfigs": 1
}
2025-06-07 23:46:36 [INFO]: Starting flight availability check {
  "service": "flight-monitor",
  "configCount": 1
}
2025-06-07 23:46:36 [INFO]: Searching flights {
  "service": "flight-monitor",
  "searchRequest": {
    "originCode": "IST",
    "destinationCode": "AYT",
    "departureDate": "2024-12-25",
    "passengers": {
      "adults": 1
    },
    "cabinClass": "ECONOMY"
  }
}
2025-06-07 23:46:36 [ERROR]: Turkish Airlines API Response Error Request failed with status code 500 {
  "service": "flight-monitor",
  "status": 500,
  "statusText": "Internal Server Error",
  "data": {
    "status": "FAILURE",
    "requestId": "000001944287d863-3c298166",
    "message": {
      "code": "TK-TK-BWS-99999",
      "description": "Unknown Error"
    },
    "data": {}
  }
}
2025-06-07 23:46:36 [ERROR]: Flight search failed {
  "service": "flight-monitor",
  "error": "Request failed with status code 500",
  "searchRequest": {
    "originCode": "IST",
    "destinationCode": "AYT",
    "departureDate": "2024-12-25",
    "passengers": {
      "adults": 1
    },
    "cabinClass": "ECONOMY"
  }
}
2025-06-07 23:46:36 [ERROR]: Failed to check flight configuration {
  "service": "flight-monitor",
  "configId": "istanbul-nevsehir-test",
  "error": "Flight search failed: Request failed with status code 500"
}
2025-06-07 23:46:36 [INFO]: Flight availability check completed {
  "service": "flight-monitor"
}
2025-06-07 23:46:36 [INFO]: ✅ Single check completed: {
  "service": "flight-monitor",
  "totalChecks": 1,
  "successfulChecks": 1,
  "failedChecks": 0,
  "flightsFound": 0,
  "notificationsSent": 0,
  "lastCheckTime": "2025-06-08T03:46:36.030Z",
  "uptime": 0
}
2025-06-07 23:47:07 [INFO]: Email service initialized {
  "service": "gmail"
}
2025-06-07 23:47:07 [INFO]: AWS SNS service initialized {
  "service": "flight-monitor"
}
2025-06-07 23:47:07 [ERROR]: Email connection test failed {
  "service": "flight-monitor",
  "error": "Invalid login: 535-5.7.8 Username and Password not accepted. For more information, go to\n535 5.7.8  https://support.google.com/mail/?p=BadCredentials d75a77b69052e-4a61117d70bsm38646881cf.34 - gsmtp"
}
2025-06-07 23:47:08 [INFO]: SMS connection test successful {
  "service": "flight-monitor"
}
2025-06-07 23:47:08 [INFO]: SMS notification sent successfully {
  "service": "flight-monitor",
  "to": "+13052820329",
  "flight": "TK2010",
  "configId": "test-notification"
}
2025-06-07 23:47:08 [ERROR]: Failed to send email notification {
  "service": "flight-monitor",
  "error": "Invalid login: 535-5.7.8 Username and Password not accepted. For more information, go to\n535 5.7.8  https://support.google.com/mail/?p=BadCredentials d75a77b69052e-4a619852fddsm39451521cf.40 - gsmtp",
  "configId": "test-notification"
}
2025-06-07 23:47:08 [ERROR]: Some notifications failed to send {
  "service": "flight-monitor",
  "error": "Invalid login: 535-5.7.8 Username and Password not accepted. For more information, go to\n535 5.7.8  https://support.google.com/mail/?p=BadCredentials d75a77b69052e-4a619852fddsm39451521cf.40 - gsmtp",
  "configId": "test-notification"
}
2025-06-07 23:53:47 [INFO]: Email service initialized {
  "service": "gmail"
}
2025-06-07 23:53:47 [INFO]: AWS SNS service initialized {
  "service": "flight-monitor"
}
2025-06-07 23:53:47 [INFO]: 🔍 Running single flight check... {
  "service": "flight-monitor"
}
2025-06-07 23:53:47 [INFO]: Flight configurations loaded {
  "service": "flight-monitor",
  "count": 2
}
2025-06-07 23:53:47 [INFO]: Flight Monitor Service initialized {
  "service": "flight-monitor",
  "configCount": 2,
  "activeConfigs": 1
}
2025-06-07 23:53:47 [INFO]: Starting flight availability check {
  "service": "flight-monitor",
  "configCount": 1
}
2025-06-07 23:53:47 [INFO]: Searching flights {
  "service": "flight-monitor",
  "searchRequest": {
    "originCode": "IST",
    "destinationCode": "AYT",
    "departureDate": "2024-12-25",
    "passengers": {
      "adults": 1
    },
    "cabinClass": "ECONOMY"
  }
}
2025-06-07 23:53:48 [ERROR]: Turkish Airlines API Response Error Request failed with status code 400 {
  "service": "flight-monitor",
  "status": 400,
  "statusText": "Bad Request",
  "data": {
    "status": "FAILURE",
    "requestId": "0000019442767783-3c844011",
    "message": {
      "code": "TK-BWS-10000",
      "description": "$.OriginDestinationInformation[0].CabinPreferences: is missing and it is not optional, $.OriginDestinationInformation[0].DepartureDateTime.WindowAfter: is missing and it is not optional, $.OriginDestinationInformation[0].DepartureDateTime.WindowBefore: is missing and it is not optional"
    },
    "data": {}
  }
}
2025-06-07 23:53:48 [ERROR]: Flight search failed {
  "service": "flight-monitor",
  "error": "Request failed with status code 400",
  "searchRequest": {
    "originCode": "IST",
    "destinationCode": "AYT",
    "departureDate": "2024-12-25",
    "passengers": {
      "adults": 1
    },
    "cabinClass": "ECONOMY"
  }
}
2025-06-07 23:53:48 [ERROR]: Failed to check flight configuration {
  "service": "flight-monitor",
  "configId": "istanbul-nevsehir-test",
  "error": "Flight search failed: Request failed with status code 400"
}
2025-06-07 23:53:48 [INFO]: Flight availability check completed {
  "service": "flight-monitor"
}
2025-06-07 23:53:48 [INFO]: ✅ Single check completed: {
  "service": "flight-monitor",
  "totalChecks": 1,
  "successfulChecks": 1,
  "failedChecks": 0,
  "flightsFound": 0,
  "notificationsSent": 0,
  "lastCheckTime": "2025-06-08T03:53:47.459Z",
  "uptime": 0
}
2025-06-07 23:54:18 [INFO]: Email service initialized {
  "service": "gmail"
}
2025-06-07 23:54:18 [INFO]: AWS SNS service initialized {
  "service": "flight-monitor"
}
2025-06-07 23:54:18 [INFO]: 🔍 Running single flight check... {
  "service": "flight-monitor"
}
2025-06-07 23:54:18 [INFO]: Flight configurations loaded {
  "service": "flight-monitor",
  "count": 2
}
2025-06-07 23:54:18 [INFO]: Flight Monitor Service initialized {
  "service": "flight-monitor",
  "configCount": 2,
  "activeConfigs": 1
}
2025-06-07 23:54:18 [INFO]: Starting flight availability check {
  "service": "flight-monitor",
  "configCount": 1
}
2025-06-07 23:54:18 [INFO]: Searching flights {
  "service": "flight-monitor",
  "searchRequest": {
    "originCode": "IST",
    "destinationCode": "AYT",
    "departureDate": "2024-12-25",
    "passengers": {
      "adults": 1
    },
    "cabinClass": "ECONOMY"
  }
}
2025-06-07 23:54:19 [ERROR]: Turkish Airlines API Response Error Request failed with status code 500 {
  "service": "flight-monitor",
  "status": 500,
  "statusText": "Internal Server Error",
  "data": {
    "status": "FAILURE",
    "requestId": "0000019442767783-3c844044",
    "message": {
      "code": "TK-TK-BWS-99999",
      "description": "Unknown Error"
    },
    "data": {}
  }
}
2025-06-07 23:54:19 [ERROR]: Flight search failed {
  "service": "flight-monitor",
  "error": "Request failed with status code 500",
  "searchRequest": {
    "originCode": "IST",
    "destinationCode": "AYT",
    "departureDate": "2024-12-25",
    "passengers": {
      "adults": 1
    },
    "cabinClass": "ECONOMY"
  }
}
2025-06-07 23:54:19 [ERROR]: Failed to check flight configuration {
  "service": "flight-monitor",
  "configId": "istanbul-nevsehir-test",
  "error": "Flight search failed: Request failed with status code 500"
}
2025-06-07 23:54:19 [INFO]: Flight availability check completed {
  "service": "flight-monitor"
}
2025-06-07 23:54:19 [INFO]: ✅ Single check completed: {
  "service": "flight-monitor",
  "totalChecks": 1,
  "successfulChecks": 1,
  "failedChecks": 0,
  "flightsFound": 0,
  "notificationsSent": 0,
  "lastCheckTime": "2025-06-08T03:54:18.580Z",
  "uptime": 0
}
2025-06-07 23:54:46 [INFO]: Email service initialized {
  "service": "gmail"
}
2025-06-07 23:54:46 [INFO]: AWS SNS service initialized {
  "service": "flight-monitor"
}
2025-06-07 23:54:46 [INFO]: 🔍 Running single flight check... {
  "service": "flight-monitor"
}
2025-06-07 23:54:46 [INFO]: Flight configurations loaded {
  "service": "flight-monitor",
  "count": 2
}
2025-06-07 23:54:46 [INFO]: Flight Monitor Service initialized {
  "service": "flight-monitor",
  "configCount": 2,
  "activeConfigs": 1
}
2025-06-07 23:54:46 [INFO]: Starting flight availability check {
  "service": "flight-monitor",
  "configCount": 1
}
2025-06-07 23:54:46 [INFO]: Searching flights {
  "service": "flight-monitor",
  "searchRequest": {
    "originCode": "IST",
    "destinationCode": "NAV",
    "departureDate": "2025-09-19",
    "passengers": {
      "adults": 1
    },
    "cabinClass": "ECONOMY"
  }
}
2025-06-07 23:54:47 [ERROR]: Turkish Airlines API Response Error Request failed with status code 500 {
  "service": "flight-monitor",
  "status": 500,
  "statusText": "Internal Server Error",
  "data": {
    "status": "FAILURE",
    "requestId": "000001944287d863-3c2984c5",
    "message": {
      "code": "TK-TK-BWS-99999",
      "description": "Unknown Error"
    },
    "data": {}
  }
}
2025-06-07 23:54:47 [ERROR]: Flight search failed {
  "service": "flight-monitor",
  "error": "Request failed with status code 500",
  "searchRequest": {
    "originCode": "IST",
    "destinationCode": "NAV",
    "departureDate": "2025-09-19",
    "passengers": {
      "adults": 1
    },
    "cabinClass": "ECONOMY"
  }
}
2025-06-07 23:54:47 [ERROR]: Failed to check flight configuration {
  "service": "flight-monitor",
  "configId": "istanbul-nevsehir-test",
  "error": "Flight search failed: Request failed with status code 500"
}
2025-06-07 23:54:47 [INFO]: Flight availability check completed {
  "service": "flight-monitor"
}
2025-06-07 23:54:47 [INFO]: ✅ Single check completed: {
  "service": "flight-monitor",
  "totalChecks": 1,
  "successfulChecks": 1,
  "failedChecks": 0,
  "flightsFound": 0,
  "notificationsSent": 0,
  "lastCheckTime": "2025-06-08T03:54:46.780Z",
  "uptime": 0
}
2025-06-07 23:58:25 [INFO]: Email service initialized {
  "service": "gmail"
}
2025-06-07 23:58:25 [INFO]: AWS SNS service initialized {
  "service": "flight-monitor"
}
2025-06-07 23:58:26 [INFO]: SMS notification sent successfully {
  "service": "flight-monitor",
  "to": "+13052820329",
  "flight": "TK2010",
  "configId": "istanbul-nevsehir-real"
}
2025-06-07 23:58:26 [ERROR]: Failed to send email notification {
  "service": "flight-monitor",
  "error": "Invalid login: 535-5.7.8 Username and Password not accepted. For more information, go to\n535 5.7.8  https://support.google.com/mail/?p=BadCredentials d75a77b69052e-4a6f82194d9sm5898811cf.79 - gsmtp",
  "configId": "istanbul-nevsehir-real"
}
2025-06-07 23:58:26 [ERROR]: Some notifications failed to send {
  "service": "flight-monitor",
  "error": "Invalid login: 535-5.7.8 Username and Password not accepted. For more information, go to\n535 5.7.8  https://support.google.com/mail/?p=BadCredentials d75a77b69052e-4a6f82194d9sm5898811cf.79 - gsmtp",
  "configId": "istanbul-nevsehir-real"
}
2025-06-08 00:00:08 [INFO]: Email service initialized {
  "service": "aws-ses"
}
2025-06-08 00:00:08 [INFO]: AWS SNS service initialized {
  "service": "flight-monitor"
}
2025-06-08 00:00:08 [INFO]: SMS notification sent successfully {
  "service": "flight-monitor",
  "to": "+13052820329",
  "flight": "TK2010",
  "configId": "istanbul-nevsehir-real"
}
2025-06-08 00:00:08 [ERROR]: Failed to send email notification {
  "service": "flight-monitor",
  "error": "Email address is not verified. The following identities failed the check in region US-EAST-1: <EMAIL>",
  "configId": "istanbul-nevsehir-real"
}
2025-06-08 00:00:08 [ERROR]: Some notifications failed to send {
  "service": "flight-monitor",
  "error": "Email address is not verified. The following identities failed the check in region US-EAST-1: <EMAIL>",
  "configId": "istanbul-nevsehir-real"
}
