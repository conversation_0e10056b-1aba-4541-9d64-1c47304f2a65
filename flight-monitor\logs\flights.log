2025-06-07 23:36:53 [INFO]: Email service initialized {
  "service": "gmail"
}
2025-06-07 23:36:53 [INFO]: AWS SNS service initialized {
  "service": "flight-monitor"
}
2025-06-07 23:36:53 [INFO]: 🔍 Running single flight check... {
  "service": "flight-monitor"
}
2025-06-07 23:36:53 [WARN]: Flight configurations file not found, creating empty configuration {
  "service": "flight-monitor"
}
2025-06-07 23:36:53 [INFO]: Flight Monitor Service initialized {
  "service": "flight-monitor",
  "configCount": 0,
  "activeConfigs": 0
}
2025-06-07 23:36:53 [INFO]: No active flight configurations to check {
  "service": "flight-monitor"
}
2025-06-07 23:36:53 [INFO]: ✅ Single check completed: {
  "service": "flight-monitor",
  "totalChecks": 0,
  "successfulChecks": 0,
  "failedChecks": 0,
  "flightsFound": 0,
  "notificationsSent": 0,
  "lastCheckTime": "",
  "uptime": 0
}
2025-06-07 23:37:21 [INFO]: Email service initialized {
  "service": "gmail"
}
2025-06-07 23:37:21 [INFO]: AWS SNS service initialized {
  "service": "flight-monitor"
}
2025-06-07 23:37:21 [INFO]: 🔍 Running single flight check... {
  "service": "flight-monitor"
}
2025-06-07 23:37:21 [INFO]: Flight configurations loaded {
  "service": "flight-monitor",
  "count": 2
}
2025-06-07 23:37:21 [INFO]: Flight Monitor Service initialized {
  "service": "flight-monitor",
  "configCount": 2,
  "activeConfigs": 1
}
2025-06-07 23:37:21 [INFO]: Starting flight availability check {
  "service": "flight-monitor",
  "configCount": 1
}
2025-06-07 23:37:21 [INFO]: Searching flights {
  "service": "flight-monitor",
  "searchRequest": {
    "originCode": "IST",
    "destinationCode": "NAV",
    "departureDate": "2025-09-19",
    "passengers": {
      "adults": 1
    },
    "cabinClass": "ECONOMY"
  }
}
2025-06-07 23:37:22 [ERROR]: Turkish Airlines API Response Error Request failed with status code 400 {
  "service": "flight-monitor",
  "status": 400,
  "statusText": "Bad Request",
  "data": "Invalid API Key"
}
2025-06-07 23:37:22 [ERROR]: Flight search failed {
  "service": "flight-monitor",
  "error": "Request failed with status code 400",
  "searchRequest": {
    "originCode": "IST",
    "destinationCode": "NAV",
    "departureDate": "2025-09-19",
    "passengers": {
      "adults": 1
    },
    "cabinClass": "ECONOMY"
  }
}
2025-06-07 23:37:22 [ERROR]: Failed to check flight configuration {
  "service": "flight-monitor",
  "configId": "istanbul-nevsehir-test",
  "error": "Flight search failed: Request failed with status code 400"
}
2025-06-07 23:37:22 [INFO]: Flight availability check completed {
  "service": "flight-monitor"
}
2025-06-07 23:37:22 [INFO]: ✅ Single check completed: {
  "service": "flight-monitor",
  "totalChecks": 1,
  "successfulChecks": 1,
  "failedChecks": 0,
  "flightsFound": 0,
  "notificationsSent": 0,
  "lastCheckTime": "2025-06-08T03:37:21.935Z",
  "uptime": 0
}
2025-06-07 23:38:24 [INFO]: Email service initialized {
  "service": "gmail"
}
2025-06-07 23:38:24 [INFO]: AWS SNS service initialized {
  "service": "flight-monitor"
}
2025-06-07 23:38:24 [INFO]: 🔍 Running single flight check... {
  "service": "flight-monitor"
}
2025-06-07 23:38:24 [INFO]: Flight configurations loaded {
  "service": "flight-monitor",
  "count": 2
}
2025-06-07 23:38:24 [INFO]: Flight Monitor Service initialized {
  "service": "flight-monitor",
  "configCount": 2,
  "activeConfigs": 1
}
2025-06-07 23:38:24 [INFO]: Starting flight availability check {
  "service": "flight-monitor",
  "configCount": 1
}
2025-06-07 23:38:24 [INFO]: Searching flights {
  "service": "flight-monitor",
  "searchRequest": {
    "originCode": "IST",
    "destinationCode": "NAV",
    "departureDate": "2025-09-19",
    "passengers": {
      "adults": 1
    },
    "cabinClass": "ECONOMY"
  }
}
2025-06-07 23:38:25 [ERROR]: Turkish Airlines API Response Error Request failed with status code 400 {
  "service": "flight-monitor",
  "status": 400,
  "statusText": "Bad Request",
  "data": "Invalid API Secret"
}
2025-06-07 23:38:25 [ERROR]: Flight search failed {
  "service": "flight-monitor",
  "error": "Request failed with status code 400",
  "searchRequest": {
    "originCode": "IST",
    "destinationCode": "NAV",
    "departureDate": "2025-09-19",
    "passengers": {
      "adults": 1
    },
    "cabinClass": "ECONOMY"
  }
}
2025-06-07 23:38:25 [ERROR]: Failed to check flight configuration {
  "service": "flight-monitor",
  "configId": "istanbul-nevsehir-test",
  "error": "Flight search failed: Request failed with status code 400"
}
2025-06-07 23:38:25 [INFO]: Flight availability check completed {
  "service": "flight-monitor"
}
2025-06-07 23:38:25 [INFO]: ✅ Single check completed: {
  "service": "flight-monitor",
  "totalChecks": 1,
  "successfulChecks": 1,
  "failedChecks": 0,
  "flightsFound": 0,
  "notificationsSent": 0,
  "lastCheckTime": "2025-06-08T03:38:24.431Z",
  "uptime": 0
}
