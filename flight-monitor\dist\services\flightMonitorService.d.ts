import { FlightMonitorConfig, MonitoringStats } from '../types/flight.types.js';
export declare class FlightMonitorService {
    private turkishAirlines;
    private notifications;
    private configs;
    private configsPath;
    private cronJob;
    private stats;
    private startTime;
    constructor();
    initialize(): Promise<void>;
    loadConfigurations(): Promise<void>;
    saveConfigurations(): Promise<void>;
    addConfiguration(config: Omit<FlightMonitorConfig, 'id' | 'createdAt'>): Promise<string>;
    removeConfiguration(id: string): Promise<boolean>;
    updateConfiguration(id: string, updates: Partial<FlightMonitorConfig>): Promise<boolean>;
    getConfigurations(): FlightMonitorConfig[];
    getConfiguration(id: string): FlightMonitorConfig | undefined;
    checkAllFlights(): Promise<void>;
    private checkFlightConfig;
    private filterMatchingFlights;
    private sendFlightNotification;
    startMonitoring(): void;
    stopMonitoring(): void;
    getStats(): MonitoringStats;
    testServices(): Promise<{
        api: boolean;
        email: boolean;
        sms: boolean;
    }>;
}
